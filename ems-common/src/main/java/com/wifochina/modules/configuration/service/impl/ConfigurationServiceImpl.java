package com.wifochina.modules.configuration.service.impl;

import cn.hutool.core.collection.CollUtil;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.configuration.entity.ConfigurationEntity;
import com.wifochina.modules.configuration.enums.ConfigurationTypeEnum;
import com.wifochina.modules.configuration.mapper.ConfigurationMapper;
import com.wifochina.modules.configuration.request.ConfigurationRequest;
import com.wifochina.modules.configuration.service.ConfigurationService;
import com.wifochina.modules.oauth.util.WebUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/16 15:32
 * @version 1.0
 */
@Service
public class ConfigurationServiceImpl extends ServiceImpl<ConfigurationMapper, ConfigurationEntity>
        implements ConfigurationService {

    @Override
    public List<ConfigurationEntity> listConfiguration(Boolean isReleased) {
        String projectId = WebUtils.projectId.get();
        return this.baseMapper.listConfiguration(projectId, isReleased);
    }

    @Override
    public void addConfiguration(ConfigurationRequest request) {
        // 校验参数
        checkRequest(request);
        // 校验配置类型的数量
        checkTypeCount(request);
        ConfigurationEntity item = new ConfigurationEntity();
        BeanUtils.copyProperties(request, item);
        item.setProjectId(WebUtils.projectId.get());
        this.save(item);
    }

    @Override
    public void updateConfiguration(ConfigurationRequest request) {
        Assert.notNull(request.getId(), "id不能为空");
        ConfigurationEntity item = new ConfigurationEntity();
        BeanUtils.copyProperties(request, item);
        this.updateById(item);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderConfiguration(List<Integer> ids) {
        Assert.notEmpty(ids, "id不能为空");
        List<ConfigurationEntity> list = listByIds(ids);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(i -> i.setIndexOrder(ids.indexOf(i.getId())));
        updateBatchById(list);
    }

    @Override
    public ConfigurationEntity detail(Integer id) {
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initProjectConfiguration(String projectId) {
        // 初始化项目配置：默认新增两个配置 整站、单机
        ConfigurationEntity wholeStation = ConfigurationEntity.builder()
                .projectId(projectId)
                .type(ConfigurationTypeEnum.WHOLE_STATION.getCode())
                .name("整站模式")
                .indexOrder(0)
                .isReleased(false)
                .createTime(new Date())
                .build();
        ConfigurationEntity standAlone = ConfigurationEntity.builder()
                .projectId(projectId)
                .type(ConfigurationTypeEnum.STAND_ALONE.getCode())
                .name("单机模式")
                .indexOrder(1)
                .isReleased(false)
                .createTime(new Date())
                .build();
        saveBatch(Arrays.asList(wholeStation, standAlone));
    }

    /** 校验配置类型的数量 */
    private void checkTypeCount(ConfigurationRequest request) {
        List<ConfigurationEntity> list = listConfiguration(null);
        if (ConfigurationTypeEnum.WHOLE_STATION.getCode() == request.getType()
                || ConfigurationTypeEnum.STAND_ALONE.getCode() == request.getType()) {
            boolean isExists =
                    list.stream().anyMatch(i -> Objects.equals(i.getType(), request.getType()));
            Assert.isTrue(!isExists, "只能添加一个整站配置和单机配置");
        }
    }

    /** 校验各类型参数 */
    private void checkRequest(ConfigurationRequest request) {
        Assert.notNull(request.getType(), "配置类型不能为空");
        ConfigurationTypeEnum type = ConfigurationTypeEnum.getEnumByCode(request.getType());
        switch (Objects.requireNonNull(type)) {
            case EMS_CONTROL:
                Assert.notNull(request.getConfigUrl(), "配置路径不能为空");
                break;
            case WHOLE_STATION:
                Assert.notNull(request.getGenerationType(), "生成方式不能为空");
                break;
            case AGC_AVC:
            case WIRING_DIAGRAM:
            case LOAD_REGULATION:
            case STAND_ALONE:
            default:
                Assert.hasLength(request.getName(), "配置名称不能为空");
                break;
        }
    }
}
