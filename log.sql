【
2.1.3
】
增加充电soc控制
云版本过渡版本
增加水冷空调
修复已知bug
1. [sql] t_group  add   enable_stop_soc	tinyint(1) NULL [0]	是否设置soc界限(true 是)(false否)
2. [ems.env] add GAREWAY_URL=http://192.168.130.49:9091

------------------------------------------------------------------------------------------------------------------------

【2.1.4
】
增加碳减排功能
增加仅在规定时间充电
增加电表报表
增加多类型电表 风电 柴油 通电桩 通用
全空屏增加soc
修复已知bug
1. [sql] INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
         (13,	NULL,	'/carbon',	'碳中和'),
         (131,	13,	'/carbon/reduction',	'碳减排'),
         (132,	13,	'/carbon/track',	'碳足迹');
2. [sql]
ALTER TABLE t_strategy
    ADD `charge_in_appoint_time` int(1) NOT NULL DEFAULT '0' COMMENT '仅在设定时间内充电'

------------------------------------------------------------------------------------------------------------------------ 【2.1.5】
调整运营收益算法
增加pv grid load 历史曲线
增加项目创建和项目分配
修复已知bug
1.[sql]
    INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
             (201,	10,	'/diagram/getDemandRate',	'需量曲线查询'),
             (202,	10,	'/diagram/getGridRate',	'并网曲线查询'),
             (203,	10,	'/diagram/getGridRate_wh',	'并网曲线分组查询'),
             (204,	10,	'/diagram/getPvRate',	'pv曲线查询'),
             (205,	10,	'/diagram/getPvRate_wh',   'pv曲线分组查询');

------------------------------------------------------------------------------------------------------------------------

【
2.1.6
】
增加新的电表类型 CEM9000 和从机号
ALTER TABLE t_ammeter
    ADD `meter_num` int(10) COMMENT 'CEM9000测控号'
增加功率控制因子
ALTER TABLE t_strategy
    ADD `power_factor_control` tinyint(1) COMMENT '功率因数控制模式开关';
ALTER TABLE t_strategy
    ADD `power_factor_first` tinyint(1) COMMENT '功率因数优先模式，关闭时为有功功率优先';
ALTER TABLE t_strategy
    ADD `power_factor_control_value` double COMMENT '功率因数控制值，0.9 - 1';

for cloud user
ALTER TABLE t_user
    ADD `all_project` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否拥有所有项目';
角色增加项目id
ALTER TABLE t_role
    ADD `project_id` varchar(255) NOT NULL DEFAULT 'common' COMMENT '项目id';

获取init_time值
设置项目create_time作为初始化时间 1653270614
for cloud project
ALTER TABLE t_project DROP `project_debug`;
ALTER TABLE t_project DROP `init_time`;
ALTER TABLE t_project
    ADD `price_proxy` tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价托管 默认false';
ALTER TABLE t_project
    ADD `fault` int(10) NOT NULL DEFAULT '0' COMMENT '项目故障';
ALTER TABLE t_project
    ADD `alarm` int(10) NOT NULL DEFAULT '0' COMMENT '项目告警';
ALTER TABLE t_project
    ADD `status` int(10) NOT NULL DEFAULT '0' COMMENT '项目状态';
ALTER TABLE t_project
    ADD `create_by` varchar(20) DEFAULT NULL COMMENT '创建者';
ALTER TABLE t_project
    ADD `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间';
ALTER TABLE t_project
    ADD `update_by` varchar(20) DEFAULT NULL COMMENT '修改者';
ALTER TABLE t_project
    ADD `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间';


------------------------------------------------------------------------------------------------------------------------

【
2.1.7
】
ALTER TABLE t_project
    ADD `duration` int(10) NOT NULL DEFAULT '8' COMMENT '时区东正西负';
ALTER TABLE t_project
    ADD `whether_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除';

CREATE TABLE `t_project_ext`
(
    `id`          varchar(128) NOT NULL COMMENT '项目扩展id',
    `area`        varchar(128)  DEFAULT NULL COMMENT '项目地点',
    `size`        varchar(128)  DEFAULT NULL COMMENT '项目规模',
    `address`     varchar(1280) DEFAULT NULL COMMENT '项目详细地址',
    `power_type`  int(10) DEFAULT NULL COMMENT '用电类型',
    `power_level` int(10) DEFAULT NULL COMMENT '电压等级',
    `longitude` double (22,0) DEFAULT NULL COMMENT '项目经度',
    `latitude` double (22,0) DEFAULT NULL COMMENT '项目维度',
    `remark`      varchar(1280) DEFAULT NULL COMMENT '项目备注',
    `ems`         int(10) NOT NULL DEFAULT '0' COMMENT 'ems数量',
    `pcs`         int(10) NOT NULL DEFAULT '0' COMMENT 'pcs数量',
    `bms`         int(10) NOT NULL DEFAULT '0' COMMENT 'bms数量',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `t_project_url`
(
    `id`           varchar(128) NOT NULL COMMENT '项目扩展id',
    `url`          varchar(256) DEFAULT NULL COMMENT '扩展url',
    `order_number` int(10) DEFAULT NULL COMMENT '序号',
    `description`  varchar(512) DEFAULT NULL COMMENT 'url描述',
    `remark`       varchar(256) DEFAULT NULL COMMENT '项目备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `t_power_level`;
CREATE TABLE `t_power_level`
(
    `uuid`        varchar(128) NOT NULL,
    `power_level` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_power_level` (`uuid`, `power_level`)
VALUES ('1', '<1kv'),
       ('2', '1kv~10kv'),
       ('3', '10kv'),
       ('4', '10kv~20kv'),
       ('5', '20kv'),
       ('6', '20kv~35kv'),
       ('7', '35kv及以下'),
       ('8', '35kv'),
       ('9', '35kv及以上'),
       ('10', '35kv~110kv'),
       ('11', '110kv'),
       ('12', '110kv~220kv'),
       ('13', '220kv'),
       ('14', '220kv及以上');

DROP TABLE IF EXISTS `t_power_type`;
CREATE TABLE `t_power_type`
(
    `uuid`       varchar(128) NOT NULL,
    `power_type` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_power_type` (`uuid`, `power_type`)
VALUES ('1', '大工业'),
       ('2', '工商业'),
       ('3', '其它');

DROP TABLE IF EXISTS `t_price_area`;
CREATE TABLE `t_price_area`
(
    `id`          varchar(128) NOT NULL COMMENT '电价配置id',
    `area`        varchar(128) NOT NULL COMMENT '项目地区',
    `power_type`  varchar(128) NOT NULL COMMENT '用电类型',
    `power_level` varchar(128) NOT NULL COMMENT '电压等级'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
ALTER TABLE t_price_area
    ADD `create_by` varchar(20) DEFAULT NULL COMMENT '创建者';
ALTER TABLE t_price_area
    ADD `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间';
ALTER TABLE t_price_area
    ADD `update_by` varchar(20) DEFAULT NULL COMMENT '修改者';
ALTER TABLE t_price_area
    ADD `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间';
ALTER TABLE t_price_area
    ADD constraint ukey primary key (`area`, `power_type`, `power_level`);

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (14, NULL, '/manage/area', '管理端-区域管理'),
       (15, NULL, '/manage/project', '管理端-项目管理'),
       (16, NULL, '/manage/user', '管理端-账户权限管理'),
       (141, 14, '/manage/area/create', '区域创建'),
       (142, 14, '/manage/area/update', '区域修改'),
       (143, 14, '/manage/area/delete', '区域删除'),
       (144, 14, '/manage/price', '电价配置'),
       (151, 15, '/manage/project/create', '项目创建'),
       (152, 15, '/manage/project/update', '项目修改'),
       (153, 15, '/manage/project/delete', '项目删除'),
       (161, 16, '/manage/user/role', '角色管理'),
       (162, 16, '/manage/user/account', '账户管理') [********]
ALTER TABLE t_project_url DROP `order`;
ALTER TABLE t_project_url
    ADD `order_num` int(10) NOT NULL DEFAULT '1' COMMENT '序号';
ALTER TABLE t_project_url
    ADD `project_id` varchar(128) NOT NULL COMMENT '项目id';

------------------------------------------------------------------------------------------------------------------------

【
2.1.8
】
1. [sql] INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
         (17,	NULL,	'/prediction',	'预测'),
         (171,	17,	'/prediction/load',	'负载功率预测');

2. [sql]
ALTER TABLE t_project
    ADD `currency` varchar(20) NOT NULL DEFAULT 'rmb' COMMENT '货币币种';
ALTER TABLE t_event_code
    ADD `event_description_en` varchar(128) COMMENT '英文事件描述';
ALTER TABLE t_event_message
    ADD `event_description_en` varchar(128) COMMENT '英文事件描述';

DROP TABLE IF EXISTS `t_version`;
CREATE TABLE `t_version`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT,
    `android_version` varchar(50) COLLATE utf8_unicode_ci  NOT NULL,
    `ios_version`     varchar(50) COLLATE utf8_unicode_ci  NOT NULL,
    `flag`            tinyint(1) NOT NULL COMMENT '0: 提醒一次 1: 每次提醒 2: 强制更新',
    `zh_cn`           varchar(500) COLLATE utf8_unicode_ci NOT NULL COMMENT '中国',
    `en_us`           varchar(500) COLLATE utf8_unicode_ci NOT NULL COMMENT '美国',
    `create_by`       varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '创建者',
    `update_by`       varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '修改者',
    `update_time`     bigint(20) DEFAULT NULL COMMENT '修改时间',
    `create_time`     bigint(20) DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (18, NULL, '/version', '版本管理')

ALTER TABLE t_user
    ADD `visit_manage` tinyint(1) NOT NULL DEFAULT '0' COMMENT '能否访问后端:false 不可以,true可以';
update t_event_code
set device_type ='PYLON Bat.'
where device_type = '派能高压电池箱';
update t_event_code
set device_type ='WH-BEC-1000/630'
where device_type = '为恒630kWPCS';
update t_event_code
set device_type ='WH-BEC-1500/1600'
where device_type = '为恒1688kWPCS';
update t_event_message
set equip_type ='PYLON Bat.'
where equip_type = '派能高压电池箱';
update t_event_message
set equip_type ='WH-BEC-1000/630'
where equip_type = '为恒630kWPCS';
update t_event_message
set equip_type ='WH-BEC-1500/1600'
where equip_type = '为恒1688kWPCS';

DROP TABLE IF EXISTS `t_strategy_history`;
CREATE TABLE `t_strategy_history`
(
    `id`                     int(11) NOT NULL AUTO_INCREMENT,
    `type`                   int(11) DEFAULT NULL,
    `week_day`               int(11) DEFAULT NULL,
    `power` double (22,0) DEFAULT NULL,
    `start_time`             time         DEFAULT NULL,
    `end_time`               time         DEFAULT NULL,
    `demand_power` double (22,0) DEFAULT NULL,
    `control_power` double (22,0) DEFAULT NULL,
    `soc` double (22,0) DEFAULT NULL,
    `anti_reflux`            int(11) DEFAULT NULL,
    `pcc_demand_power` double (22,0) DEFAULT NULL,
    `pcc_control` double (22,0) DEFAULT NULL,
    `group_id`               varchar(128) DEFAULT NULL,
    `project_id`             varchar(128) DEFAULT NULL,
    `version`                varchar(128) DEFAULT NULL,
    `create_time`            bigint(20) DEFAULT NULL,
    `create_by`              text,
    `update_time`            bigint(20) DEFAULT NULL,
    `update_by`              text,
    `priority`               int(11) DEFAULT NULL,
    `back_flow_limit_power` double (22,0) DEFAULT NULL COMMENT '防逆流功率值',
    `charge_in_appoint_time` int(1) NOT NULL DEFAULT '0' COMMENT '仅在设定时间内充电',
    `power_factor_control`   tinyint(1) DEFAULT NULL COMMENT '功率因数控制模式开关',
    `power_factor_first`     tinyint(1) DEFAULT NULL COMMENT '功率因数优先模式，关闭时为有功功率优先',
    `power_factor_control_value` double DEFAULT NULL COMMENT '功率因数控制值，0.9 - 1',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `t_favourite_project`;
CREATE TABLE `t_favourite_project`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `project_id`   varchar(128) DEFAULT NULL COMMENT '项目id',
    `user_id`      varchar(128) DEFAULT NULL COMMENT '用户id',
    `focus_status` varchar(128) DEFAULT NULL COMMENT '关注状态',
    `sms`          varchar(128) DEFAULT NULL COMMENT '短信',
    `phone`        varchar(128) DEFAULT NULL COMMENT '电话',
    `wechat`       varchar(128) DEFAULT NULL COMMENT '微信',
    `other`        varchar(128) DEFAULT NULL,
    `create_by`    varchar(20)  DEFAULT NULL COMMENT '创建者',
    `create_time`  bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`  bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (19, NULL, '/manage/favourite', '管理端-重点关注项目'),
       (191, 19, '/manage/favourite/list', '重点关注项目列表'),
       (192, 19, '/manage/favourite/add', '重点关注项目增加'),
       (193, 19, '/manage/favourite/delete', '重点关注项目删除'),
       (194, 19, '/manage/favourite/edit', '重点关注项目修改')

DROP TABLE IF EXISTS `t_power_level`;
CREATE TABLE `t_power_level`
(
    `uuid`        varchar(128) NOT NULL,
    `power_level` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_power_level` (`uuid`, `power_level`)
VALUES ('1', '<1kv'),
       ('2', '1kv~10kv'),
       ('3', '20kv'),
       ('4', '35kv'),
       ('5', '110kv');


DROP TABLE IF EXISTS `t_power_type`;
CREATE TABLE `t_power_type`
(
    `uuid`          varchar(128) NOT NULL,
    `power_type`    varchar(128) NOT NULL,
    `power_type_en` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_power_type` (`uuid`, `power_type`, `power_type_en`)
VALUES ('1', '大工业', 'Large Industrial'),
       ('2', '工商业', 'Industrial and Commercial'),
       ('3', '协议电价', 'Power Purchase Agreement'),
       ('4', '其它', 'Other');

important
==> 需要更新 event-code 1118

【2.1.9
】【1.2.0
】

ALTER TABLE t_device
    ADD `code` varchar(128) COMMENT '设备二维码code';
ALTER TABLE t_project_ext
    ADD `enable_radiation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否采集光伏数据:false 不采集,true采集';
ALTER TABLE t_project_ext
    ADD `enable_weather` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否采集天气数据:false 不采集,true采集';
ALTER TABLE t_project_ext
    ADD `enable_prediction` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否采集功率预测数据:false 不采集,true采集';

ALTER TABLE t_group
    add `enable_load_grid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否节日负载电表:true 是,false否';

ALTER TABLE t_project_ext
    ADD `area_id` varchar(128) NULL COMMENT  '区域id';
ALTER TABLE t_group
    ADD `enable_weather` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否展示天气数据:false 不展示,true展示';

ALTER TABLE t_group
    ADD `pv_prediction` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启光伏预测:0 不预测, 1:DeepAR, 2:DeepTFT';
ALTER TABLE t_group
    ADD `load_prediction` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启负载预测:0 不预测, 1:DeepAR, 2:DeepTFT';

ALTER TABLE t_user
    ADD `name` varchar(128) NULL  COMMENT '姓名';



INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (10101, 10, '/diagram/getPcsRate', 'pcs功率曲线'),
       (10102, 10, '/diagram/getGridFactorRate', '电网功率因数曲线'),
       (311, 31, '/group/group/add', '分组增加'),
       (312, 31, '/group/group/delete', '分组删除'),
       (313, 31, '/group/group/edit', '分组修改'),
       (314, 31, '/group/group/query', '分组查询'),
       (321, 32, '/group/ammeter/add', '电表增加'),
       (322, 32, '/group/ammeter/delete', '电表删除'),
       (323, 32, '/group/ammeter/edit', '电表修改'),
       (324, 32, '/group/ammeter/query', '电表查询'),
       (331, 33, '/group/device/add', '设备增加'),
       (332, 33, '/group/device/delete', '设备删除'),
       (333, 33, '/group/device/edit', '设备修改'),
       (334, 33, '/group/device/query', '设备查询'),
       (335, 33, '/group/device/change', '设备运行'),
       (336, 33, '/group/device/detail', '设备详情'),
       (341, 34, '/group/controller/edit', '协调控制器修改'),
       (342, 34, '/group/controller/query', '协调控制器查询'),
       (41, 4, '/strategy/edit', '分组策略修改'),
       (42, 4, '/strategy/query', '分组策略查询'),
       (43, 4, '/strategy/import', '分组策略导入'),
       (44, 4, '/strategy/upload', '全部策略下发'),
       (45, 4, '/strategy/time/add', '时间策略增加'),
       (46, 4, '/strategy/time/delete', '时间策略删除'),
       (47, 4, '/strategy/time/edit', '时间策略修改'),
       (48, 4, '/strategy/time/import', '时间策略导入'),
       (123, 12, '/system/control', '设备控制');


ALTER TABLE t_project
    ADD `whether_accessible` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可以访问本地';
ALTER TABLE t_project
    ADD `local_project_id` varchar(128) NULL COMMENT '本地项目id';
ALTER TABLE t_project
    ADD `whether_backup` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否展示备电: 默认false 不备电,true备电';
ALTER TABLE t_project_ext
    ADD `prediction_path` varchar(128) NULL COMMENT  '预测路径名称';


【
1.2.1
】
ALTER TABLE t_device
    ADD `unreal` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否虚拟,默认不虚拟false';
ALTER TABLE t_device
    ADD `pcs_index` int(10) NULL COMMENT '虚拟设备的pcs序号,0,1,2,3,4';
ALTER TABLE t_device
    ADD `output_history_init` double COMMENT '电量输出初始值';
ALTER TABLE t_device
    ADD `input_history_init` double COMMENT '电量输入初始值';
ALTER TABLE t_ammeter
    ADD `output_history_init` double COMMENT '电量输出初始值';
ALTER TABLE t_ammeter
    ADD `input_history_init` double COMMENT '电量输入初始值';
ALTER TABLE t_group
    ADD `external_controller_offset` int(10) COMMENT '外部控制器偏移量';
ALTER TABLE t_group
    ADD `enable_ems` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接入储能';
ALTER TABLE t_group
    ADD `enable_electric_grid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接入并网';
ALTER TABLE t_device
    ADD `power_capacity_percent` double COMMENT '功率分配百分比';
ALTER TABLE t_group
    ADD `enable_show_electric_quantity` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示电网,默认展示';


INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (113, 11, '/user/log', '操作日志'),
       (163, 16, '/manage/user/log', '操作日志');
ALTER TABLE t_group
    ADD `pcs_code` varchar(128) NULL COMMENT 'pcs型号';

ALTER TABLE t_event_message
    ADD INDEX `device_id_status_event_type_create_time` (`device_id`,`status`,`event_type`,`create_time`) 【1.2.2】
ALTER TABLE t_group
    ADD `wind_power_model` int(10) COMMENT '风电发电模式 1全额上网 2自发自用余量上网';
ALTER TABLE t_group
    ADD `enable_wind_power_generation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否接入风电';
ALTER TABLE t_group
    ADD `enable_wood_power_generation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否接入柴发';
ALTER TABLE t_group
    ADD `enable_charging_pile_power` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否接入充电桩';
ALTER TABLE t_group
    ADD `enable_gas_power_generation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否接入燃气发电机';
ALTER TABLE t_electric_price
    ADD `wind_self_price` double NULL  COMMENT '风电自用价格';
ALTER TABLE t_electric_price
    ADD `wind_df_price` double NULL  COMMENT '风电脱硫标杆价格';
ALTER TABLE t_electric_price
    ADD `wind_subsidy_price` double NULL  COMMENT '风电国家补贴价格';
ALTER TABLE t_electric_price
    ADD `wind_price` double NULL  COMMENT '风电发电价格';
ALTER TABLE t_group
    ADD `enable_load` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接入负载';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (10103, 10, '/diagram/getWindRate', '风电功率'),
       (10104, 10, '/diagram/getWindRate_wh', '风电功率-分组'),
       (10105, 10, '/diagram/getDieselRate', '柴发功率'),
       (10106, 10, '/diagram/getDieselRate_wh', '柴发功率-分组'),
       (10107, 10, '/diagram/getPileRate', '充电桩功率'),
       (10108, 10, '/diagram/getPileRate_wh', '充电桩功率-分组'),
       (10109, 10, '/diagram/getGasRate', '燃气功率'),
       (10110, 10, '/diagram/getGasRate_wh', '燃气功率-分组'),
       (60001, 6, '/heatMap/windDischarge', '风电'),
       (60002, 6, '/heatMap/windDischarge_wh', '风电-分组'),
       (60003, 6, '/heatMap/dieselDischarge', '柴发'),
       (60004, 6, '/heatMap/dieselDischarge_wh', '柴发-分组'),
       (60005, 6, '/heatMap/pileDischarge', '充电桩'),
       (60006, 6, '/heatMap/pileDischarge_wh', '充电桩-分组'),
       (60007, 6, '/heatMap/gasDischarge', '燃气'),
       (60008, 6, '/heatMap/gasDischarge_wh', '燃气-分组');


【
1.2.3
】
DROP TABLE IF EXISTS `t_prediction_config`;
CREATE TABLE `t_prediction_config`
(
    `project_id`     varchar(128) NOT NULL COMMENT '项目id',
    `type`           varchar(128) NOT NULL COMMENT '类型',
    `week`           tinyint(4) NOT NULL COMMENT '星期',
    `file_index`     varchar(128) NOT NULL COMMENT '文件索引',
    `model_24hours`  varchar(128) NOT NULL COMMENT '24小时模式',
    `config_24hours` varchar(512)  DEFAULT NULL COMMENT '24小时配置',
    `model_48hours`  varchar(128) NOT NULL COMMENT '48小时模式',
    `config_48hours` varchar(1048) DEFAULT NULL COMMENT '48小时配置',
    `create_by`      varchar(20)   DEFAULT NULL COMMENT '创建者',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`      varchar(20)   DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`project_id`, `type`, `week`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `t_prediction_manage`;
CREATE TABLE `t_prediction_manage`
(
    `project_id`  varchar(128) NOT NULL COMMENT '项目id',
    `type`        varchar(128) NOT NULL COMMENT '类型',
    `create_by`   varchar(20) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(20) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`project_id`, `type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (21, NULL, '/manage/prediction', '管理端-预测配置'),
       (2101, 21, '/manage/prediction/addConfig', '增加项目预测配置'),
       (2102, 21, '/manage/prediction/deleteConfig', '删除项目预测配置'),
       (2103, 21, '/manage/prediction/queryConfig', '查询项目预测配置'),
       (2104, 21, '/manage/prediction/editConfig', '修改项目预测配置'),
       (1101, 10, '/diagram/getLoadRate_wh', '负载曲线-分组'),
       (1102, 10, '/diagram/selfDefine', '自定义曲线');


ALTER TABLE t_event_message
    ADD INDEX `device_id_status_event_type_create_time` (`device_id`,`status`,`event_type`,`create_time`) 【1.2.4】
ALTER TABLE t_device
    ADD `maintain` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否维护中，默认为false 不维护';
ALTER TABLE t_device
    ADD `show_gps` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否展示gps，默认为false 不展示';

CREATE TABLE `t_controllable`
(
    `id`           varchar(128) NOT NULL COMMENT 'uuid',
    `name`         char(128)    DEFAULT NULL COMMENT '名称',
    `type`         varchar(20)  DEFAULT NULL COMMENT '类型：Load、IO、Power',
    `vendor`       varchar(30)  DEFAULT NULL COMMENT '型号',
    `ip`           varchar(50)  DEFAULT NULL COMMENT 'ip',
    `port`         int(11) DEFAULT NULL COMMENT '端口',
    `soc_high` double DEFAULT NULL COMMENT 'soc上限',
    `soc_low` double DEFAULT NULL COMMENT 'soc下限',
    `grid_disconnect_start_soc` double DEFAULT NULL COMMENT 'soc上限操作（high,low）',
    `grid_disconnect_stop_soc` double DEFAULT NULL COMMENT 'soc下限操作  (high,low)',
    `power` double DEFAULT NULL COMMENT '功率(kw)',
    `speed` double DEFAULT NULL COMMENT '速度(kw/s)',
    `operate_high` varchar(10)  DEFAULT NULL COMMENT 'soc上限操作（high,low）',
    `operate_low`  varchar(10)  DEFAULT NULL COMMENT 'soc下限操作  (high,low)',
    `project_id`   varchar(128) DEFAULT NULL COMMENT '项目id',
    `create_by`    tinytext COMMENT '创建者',
    `create_time`  bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`    tinytext COMMENT '更新者',
    `update_time`  bigint(20) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='协调控制器';

CREATE TABLE `t_group_controllable`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT '关系id',
    `group_id`        varchar(128) NOT NULL COMMENT '分组id',
    `controllable_id` varchar(128) NOT NULL COMMENT '可控设备id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `t_group_ammeter_pk` (`group_id`,`controllable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分组与可控设备关联表';


INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (35, 3, '/group/controllable', '可控设备管理'),
       (351, 35, '/group/controllable/add', '可控设备增加'),
       (352, 35, '/group/controllable/edit', '可控设备修改'),
       (353, 35, '/group/controllable/delete', '可控设备删除'),
       (354, 35, '/group/controllable/query', '可控设备查询'),
       (125, 12, '/system/maintain', '设备维护');
ALTER TABLE t_event_message
    ADD `maintain` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否维护中，默认为false 不维护';
ALTER TABLE t_ammeter
    ADD `controllable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否测控电表，默认为false 不是测控电表';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (75, 7, '/monitor/controllableStatus', '测控设备监控');
ALTER TABLE t_ammeter
    ADD `maintain` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否维护中，默认为false 不维护';
ALTER TABLE t_ammeter
    ADD `income` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否计算收益，默认为false 不计算';
ALTER TABLE t_device
    ADD `income` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否计算收益，默认为false 不计算';

ALTER TABLE t_group
    ADD `direct_power_auto_control` tinyint(1)  NULL COMMENT '外部控制模式切换：是否根据远端控制自动切换，false为手动';
ALTER TABLE t_group
    ADD `direct_power_control_iec104_enable_ioa` bigint(20)  NULL  COMMENT '外部控制模式自动时，切换开关的104遥控点位';
ALTER TABLE t_group
    ADD `direct_power_control_iec104_common_addr` bigint(20)  NULL  COMMENT '外部控制模式下，104协议中，公共地址';
ALTER TABLE t_group
    ADD `direct_power_control_iec104_active_ioa` bigint(20)  NULL  COMMENT '外部控制模式下，104协议中，有功功率摇调地址';
ALTER TABLE t_group
    ADD `direct_power_control_iec104_reactive_ioa` bigint(20)  NULL  COMMENT '外部控制模式下，104协议中，无功功率摇调地址';
ALTER TABLE t_group
    ADD `direct_power_control_modbus_active_addr` bigint(20)  NULL  COMMENT 'modbus协议中，有功功率寄存器地址(两个寄存器)';
ALTER TABLE t_group
    ADD `direct_power_control_modbus_reactive_addr` bigint(20)  NULL  COMMENT 'modbus协议中，无功功率寄存器地址(两个寄存器)';
ALTER TABLE t_controllable
    ADD `status` int(1) NOT NULL DEFAULT '0' COMMENT '1已连接、2未连接、0未测试';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (126, 12, '/system/rest', '设备复位');

ALTER TABLE t_strategy
    ADD `discharge_in_appoint_time` int(1)  NULL DEFAULT '0' COMMENT '仅在设定时间放电充电';
ALTER TABLE t_strategy_history
    ADD `discharge_in_appoint_time` int(1)  NULL DEFAULT '0' COMMENT '仅在设定时间放电充电';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (172, 17, '/prediction/recommend', '智能推荐');

ALTER TABLE t_ammeter
    ADD `use_low_side` tinyint(1) COMMENT '高压还是低压侧';
ALTER TABLE t_ammeter
    ADD `pt_ratio` double COMMENT 'pt比';
ALTER TABLE t_ammeter
    ADD `ct_ratio` double COMMENT 'ct比';
ALTER TABLE t_project
    ADD `province` int(10) NOT NULL DEFAULT '1' COMMENT '项目省份';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (1103, 10, '/diagram/getAcCurrentRate', '交流电流曲线'),
       (1104, 10, '/diagram/getDcCurrentRate', '直流电流曲线');

CREATE TABLE `t_meter_event_code`
(
    `id`                           int(11) NOT NULL AUTO_INCREMENT,
    `project_id`                   varchar(50)  DEFAULT NULL COMMENT '对应的点位字段',
    `project_name`                 varchar(50)  DEFAULT NULL COMMENT '设备类型名称',
    `meter_type`                   varchar(20)  DEFAULT NULL COMMENT '代表类型CEM9000和BHE336',
    `event_level`                  varchar(20)  DEFAULT NULL COMMENT '事件等级Fault、State、Alarm',
    `bit_offset`                   int(11) DEFAULT NULL COMMENT 'BIT码偏移量',
    `bit_value`                    int(11) DEFAULT NULL COMMENT 'BIT码',
    `description`                  varchar(80)  DEFAULT NULL COMMENT '事件描述',
    `description_en`               varchar(128) DEFAULT NULL COMMENT '英文事件描述',
    `digital_0_analog_1_control_2` int(11) DEFAULT NULL COMMENT 'BIT位是0或者1有效',
    `create_time`                  bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`                    varchar(32)  DEFAULT NULL COMMENT '创建者',
    `update_time`                  bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`                    varchar(32)  DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='时间信息告警信息表';
--- t_event_message index
---  project_id, status, event_type, create_time, maintain, device_id
【
1.2.5
】
CREATE TABLE `t_ems_data_count`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `project_id`  varchar(128) DEFAULT NULL COMMENT '项目id',
    `time`        bigint(20) NOT NULL COMMENT '数据时间',
    `state`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `ems_count`   Int(10) NOT NULL DEFAULT '0' COMMENT 'ems总数',
    `data_count`  Int(10) NOT NULL DEFAULT '0' COMMENT '当天数据总数',
    `create_by`   tinytext COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   tinytext COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据状态';

CREATE TABLE `t_ems_data_remedies`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `project_id`  varchar(128) DEFAULT NULL COMMENT '项目id',
    `time`        bigint(20) NOT NULL COMMENT '数据时间',
    `peak_history_output_energy` double NOT NULL DEFAULT '0' COMMENT '峰历史输出能量',
    `vally_history_output_energy` double NOT NULL DEFAULT '0' COMMENT '谷历史输出能量',
    `flat_history_output_energy` double NOT NULL DEFAULT '0' COMMENT '平历史输出能量',
    `tip_history_output_energy` double NOT NULL DEFAULT '0' COMMENT '尖历史输出能量',
    `deep_vally_history_output_energy` double NOT NULL DEFAULT '0' COMMENT '深谷历史输出能量',
    `peak_history_input_energy` double NOT NULL DEFAULT '0' COMMENT '峰历史输入能量',
    `vally_history_input_energy` double NOT NULL DEFAULT '0' COMMENT '谷历史输入能量',
    `flat_history_input_energy` double NOT NULL DEFAULT '0' COMMENT '平历史输入能量',
    `tip_history_input_energy` double NOT NULL DEFAULT '0' COMMENT '尖历史输入能量',
    `deep_vally_history_input_energy` double NOT NULL DEFAULT '0' COMMENT '深谷历史输入能量',
    `create_by`   tinytext COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   tinytext COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    `year`        int(4) NOT NULL COMMENT '年',
    `month`       int(2) NOT NULL COMMENT '月',
    `day`         int(2) NOT NULL COMMENT '日',
    PRIMARY KEY (`id`),
    UNIQUE KEY `time_project_id` (`time`,`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据状态';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (1611, 161, '/manage/user/role/add', '角色增加'),
       (1612, 161, '/manage/user/role/delete', '角色删除'),
       (1613, 161, '/manage/user/role/edit', '角色修改'),
       (1614, 161, '/manage/user/role/query', '角色查询');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (1621, 162, '/manage/user/account/add', '账户增加'),
       (1622, 162, '/manage/user/account/delete', '账户删除'),
       (1623, 162, '/manage/user/account/edit', '账户修改'),
       (1624, 162, '/manage/user/account/query', '账户查询');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (22, NULL, '/remedies', '数据补充'),
       (221, 22, '/remedies/add', '补充数据添加'),
       (222, 22, '/remedies/update', '补充数据修改'),
       (223, 22, '/remedies/delete', '补充数据删除'),
       (224, 22, '/remedies/query', '补充数据查询');
ALTER TABLE t_ammeter
    ADD `meter_reading` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否抄表，默认为false 不抄表';

CREATE TABLE `t_electric`
(
    `id`         int(32) NOT NULL AUTO_INCREMENT,
    `project_id` varchar(128) NOT NULL COMMENT '项目id',
    `device_id`  varchar(128) NOT NULL COMMENT '设备Id',
    `time`       bigint(20) NOT NULL COMMENT '数据时间',
    `year`       int(4) NOT NULL COMMENT '年',
    `month`      int(2) NOT NULL COMMENT '月',
    `day`        int(2) NOT NULL COMMENT '日',
    `peak_charge_quantity` double (30,15) DEFAULT NULL COMMENT '峰段充电量',
    `vally_charge_quantity` double (30,15) DEFAULT NULL COMMENT '谷段充电量',
    `flat_charge_quantity` double (30,15) DEFAULT NULL COMMENT '平段充电量',
    `tip_charge_quantity` double (30,15) DEFAULT NULL COMMENT '尖峰充电量',
    `deep_vally_charge_quantity` double (30,15) DEFAULT NULL COMMENT '深谷充电量',
    `peak_charge_cost` double (30,15) DEFAULT NULL COMMENT '峰段成本',
    `vally_charge_cost` double (30,15) DEFAULT NULL COMMENT '谷段成本',
    `flat_charge_cost` double (30,15) DEFAULT NULL COMMENT '平段成本',
    `tip_charge_cost` double (30,15) DEFAULT NULL COMMENT '尖峰成本',
    `deep_vally_charge_cost` double (30,15) DEFAULT NULL COMMENT '深谷成本',
    `peak_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '尖峰放电量',
    `vally_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '谷段放电量',
    `flat_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '平段放电量',
    `tip_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '尖峰放电量',
    `deep_vally_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '深谷放电量',
    `peak_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '峰段收益',
    `vally_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '谷段收益',
    `flat_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '平段收益',
    `tip_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '尖峰收益',
    `deep_vally_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '深谷收益',
    `peak_price` double (30,15) DEFAULT NULL COMMENT '峰价格',
    `vally_price` double (30,15) DEFAULT NULL COMMENT '谷价格',
    `flat_price` double (30,15) DEFAULT NULL COMMENT '平价格',
    `tip_price` double (30,15) DEFAULT NULL COMMENT '尖价格',
    `deep_vally_price` double (30,15) DEFAULT NULL COMMENT '深谷价格',
    `total_charge_cost` double (30,15) DEFAULT NULL COMMENT '总成本',
    `total_charge_quantity` double (30,15) DEFAULT NULL COMMENT '总充电量',
    `total_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '总放电量',
    `total_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '总放电收益',
    `total_benefit` double (30,15) DEFAULT NULL COMMENT '总收益',
    PRIMARY KEY (`project_id`, `device_id`, `time`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `t_camera`
(
    `id`          varchar(128) NOT NULL COMMENT '摄像头识符uuid',
    `name`        varchar(128) DEFAULT NULL COMMENT '摄像头名称',
    `ip`          varchar(32)  DEFAULT NULL COMMENT '摄像头ip',
    `port`        int(10) DEFAULT NULL COMMENT '摄像头端口',
    `vendor`      varchar(256) DEFAULT NULL COMMENT '摄像头销售商',
    `type`        varchar(256) DEFAULT NULL COMMENT '摄像头型号',
    `username`    varchar(256) DEFAULT NULL COMMENT '用户名',
    `password`    varchar(256) DEFAULT NULL COMMENT '密码',
    `auth_type`   varchar(256) DEFAULT NULL COMMENT '认证类型(basic、digest）',
    `status`      int(1) DEFAULT NULL COMMENT '1已连接、2未连接、0未测试',
    `project_id`  varchar(128) DEFAULT NULL COMMENT '项目id',
    `create_by`   varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='摄像头';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (36, 3, '/group/camera', '摄像头管理'),
       (361, 36, '/group/camera/add', '摄像头增加'),
       (362, 36, '/group/camera/edit', '摄像头修改'),
       (363, 36, '/group/camera/delete', '摄像头删除'),
       (364, 36, '/group/camera/query', '摄像头查询');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (97, 9, '/report/reading/month', '抄表月报'),
       (98, 9, '/report/reading/year', '抄表年报');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (53, 5, '/notice/add', '收益通知'),
       (531, 53, '/notice/add', '通知增加'),
       (532, 53, '/notice/edit', '通知修改'),
       (533, 53, '/notice/delete', '通知删除'),
       (534, 53, '/notice/query', '通知查询');


ALTER TABLE t_device
    ADD `index` int(10)  NULL  COMMENT '序号';
ALTER TABLE t_ammeter
    ADD `index` int(10)  NULL  COMMENT '序号';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (1001, 1, '/homePage/showGps', '显示gps');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (76, 7, '/monitor/camera', '摄像头监控');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (921, 92, '/report/month/missData', '异常数据查看');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (922, 92, '/report/month/query', '电站月报查看');


ALTER TABLE t_ammeter drop `pt_ratio`;
ALTER TABLE t_ammeter drop `ct_ratio`;

ALTER TABLE t_ammeter
    ADD `high_ct_ratio` double COMMENT '高压侧 CT比';
ALTER TABLE t_ammeter
    ADD `high_pt_ratio` double COMMENT '高压侧 PT比';
ALTER TABLE t_ammeter
    ADD `high_protect_ct_ratio` double COMMENT '高压侧保护CT比';
ALTER TABLE t_ammeter
    ADD `low_ct_ratio` double COMMENT '低压侧 CT比';
ALTER TABLE t_ammeter
    ADD `low_pt_ratio` double COMMENT '低压侧 PT比';


ALTER TABLE t_project
    ADD `cloud_large_screen` tinyint(1) NOT NULL DEFAULT '0' COMMENT '云端大屏展示 默认false';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (23, NULL, '/largeScreen', '项目大屏');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (24, NULL, '/cloudlargeScreen', '云端大屏');

CREATE TABLE `t_notice`
(
    `id`          varchar(128) NOT NULL COMMENT '通知识符uuid',
    `type`        varchar(128) DEFAULT NULL COMMENT '通知类型（所有、储能、光伏、风电、需量）',
    `email`       varchar(32)  DEFAULT NULL COMMENT '邮箱',
    `project_id`  varchar(128) DEFAULT NULL COMMENT '项目id',
    `create_by`   varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint       DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint       DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


ALTER TABLE t_project
    ADD `un_confirm_all` tinyint(1) NOT NULL DEFAULT '0' COMMENT '未确认错误';
ALTER TABLE t_project
    ADD `un_confirm_not_maintain` tinyint(1) NOT NULL DEFAULT '0' COMMENT '排除维护的未确认错误';

【
1.3.1
】
ALTER TABLE t_ammeter
    ADD `dc_meter` tinyint(1) NULL DEFAULT '0' COMMENT '是否是直流电表';

ALTER TABLE t_group
    add `has_fire_fighting` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有消防:true 是,false否';
ALTER TABLE t_group
    add `has_sts` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有STS:true 是,false否';
ALTER TABLE t_group
    add `has_dcdc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有DCDC:true 是,false否';
ALTER TABLE t_group
    add `show_type` int(1) NOT NULL DEFAULT '0' COMMENT '0代表ems, 1代表电表';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (77, 7, '/monitor/controllableAction', '测控电表控制操作');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (78, 7, '/monitor/realTimeMeterMonitor', '电表实时运行监控');

ALTER TABLE t_project
    ADD `large_screen` tinyint(1) NOT NULL DEFAULT '0' COMMENT '项目大屏展示 默认false';


INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (206, 10, '/diagram/getEfficiencyRate', '系统效率曲线');

ALTER TABLE t_event_message
    ADD `whether_delete` tinyint(1) NULL DEFAULT '0' COMMENT '是否删除';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (81, 8, '/event/confirm', '事件确认');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (82, 8, '/event/delete', '故障删除');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (79, 7, '/monitor/dcdcStatus', 'dcdc监控'),
       (711, 7, '/monitor/statesStatus', 'states监控');



ALTER TABLE t_group
    ADD `pv_profit_controller` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'PV收益计算控制';
ALTER TABLE t_project_ext
    ADD `enable_etl` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启采集 默认false';

--- influxdb
datagram_rate_mean_1m ---- test ----

import "influxdata/influxdb/tasks"

option task = {
    name: "datagram_rate_mean_1m",
    every: 5m,
    offset: 1m,
}

from(bucket: "ems")
    |> range(start: tasks.lastSuccess(orTime: -task.every))
    |> filter(fn: (r) => r._measurement == "T1_5s" or r._measurement == "T3_5s")
    |> filter(fn: (r) => r["_field"] == "ems_ac_active_power_pos" or r["_field"] == "ac_active_power" or r["_field"] == "ac_reactive_power" or r["_field"] == "ems_ac_active_power_neg" or r["_field"] == "ems_ac_active_power" or r["_field"] == "ac_current" or r["_field"] == "ac_currents_0" or r["_field"] == "ac_currents_1" or r["_field"] == "ac_currents_2" or r["_field"] == "ac_active_powers_0" or r["_field"] == "ac_active_powers_1" or r["_field"] == "ac_active_powers_2" or r["_field"] == "ac_reactive_powers_0" or r["_field"] == "ac_reactive_powers_1" or r["_field"] == "ac_reactive_powers_2" or r["_field"] == "dc_voltage" or r["_field"] == "dc_power" or r["_field"] == "frequency" or r["_field"] == "ac_voltage" or r["_field"] == "ac_voltages_0" or r["_field"] == "ac_voltages_1" or r["_field"] == "ac_voltages_2")
    |> aggregateWindow(every: 1m, fn: mean)
    |> to(bucket: "mean")

【1.3.2
】

ALTER TABLE t_favourite_project
    ADD `email` tinyint(1) NULL DEFAULT '0' COMMENT '是否邮箱通知';
ALTER TABLE t_group
    ALTER COLUMN has_fire_fighting SET DEFAULT 1;

CREATE TABLE `t_country`
(
    `id`         int         NOT NULL,
    `datacenter` int         NOT NULL,
    `phone_code` varchar(10) NOT NULL,
    `zh_cn`      varchar(128) DEFAULT NULL,
    `en_us`      varchar(128) DEFAULT NULL,
    `de_de`      varchar(128) DEFAULT NULL,
    `nl_nl`      varchar(128) DEFAULT NULL,
    `fr_fr`      varchar(128) DEFAULT NULL,
    `es_es`      varchar(128) DEFAULT NULL,
    `pt_pt`      varchar(128) DEFAULT NULL,
    `it_it`      varchar(128) DEFAULT NULL,
    `pl_pl`      varchar(128) DEFAULT NULL
) ENGINE='InnoDB' COLLATE 'utf8_general_ci';

ALTER TABLE t_project
    ADD `country` int(10)  NULL COMMENT '国家id';
ALTER TABLE t_project
    ADD `data_center` int(10) NULL COMMENT '数据分区';
ALTER TABLE t_project
    ADD `timezone` varchar(255) NOT NULL DEFAULT 'Asia/Shanghai' COMMENT '项目时区';

CREATE TABLE `t_time_zone`
(
    `id`       int(11) NOT NULL AUTO_INCREMENT,
    `timezone` varchar(100) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
    `name`     varchar(20) COLLATE utf8_unicode_ci  NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE `t_area`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `pid`          int(11) DEFAULT NULL COMMENT '父id',
    `city_name`    varchar(100) DEFAULT NULL COMMENT '简称',
    `name`         varchar(100) DEFAULT NULL COMMENT '名称',
    `merger_name`  varchar(255) DEFAULT NULL COMMENT '全称',
    `type`         tinyint(4) unsigned DEFAULT '0' COMMENT '层级 1 2 3 省市区县',
    `city_name_en` varchar(100) DEFAULT NULL COMMENT '拼音',
    `code`         varchar(100) DEFAULT NULL COMMENT '长途区号',
    `zip_code`     varchar(100) DEFAULT NULL COMMENT '邮编',
    `first`        varchar(50)  DEFAULT NULL COMMENT '首字母',
    `lng`          varchar(100) DEFAULT NULL COMMENT '经度',
    `lat`          varchar(100) DEFAULT NULL COMMENT '纬度',
    PRIMARY KEY (`id`) USING BTREE,
    KEY            `name,type` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


【
1.3.2-dcdc
】
// -------------dcdc---------
ALTER TABLE t_device
    ADD `has_sts` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有STS';
ALTER TABLE t_device
    ADD `has_dcdc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有dcdc';
ALTER TABLE t_device
    ADD `pv_output_history_init` double COMMENT 'pv表初始放电量初始值';
ALTER TABLE t_device
    ADD `pv_input_history_init` double COMMENT 'pv表初始充电量初始值';
ALTER TABLE t_group
    ADD `show_battery_electricity` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示电池电量';
ALTER TABLE t_group
    ADD `dcdc_profit_controller` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否开启dcdc收益开关';

update t_group
set `show_battery_electricity` = 1;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (79, 7, '/monitor/dcdcStatus', 'DCDC运行监控');



【
1.3.3
】
CREATE TABLE `t_demand`
(
    `id`         int(11) NOT NULL COMMENT 'id',
    `group_id`   varchar(128) NOT NULL COMMENT '分组id',
    `project_id` varchar(128) NOT NULL COMMENT '项目id',
    `demand` double unsigned NOT NULL COMMENT '需量值',
    `time`       int(10) unsigned NOT NULL COMMENT '发生时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `t_demand_log`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `group_id`       varchar(128) NOT NULL COMMENT '分组id',
    `project_id`     varchar(128) NOT NULL COMMENT '项目id',
    `description`    varchar(128) NOT NULL COMMENT '描述',
    `description_en` varchar(128) DEFAULT NULL COMMENT '英文描述',
    `type`           int(1) NOT NULL COMMENT '1 需量超了 2设置控制需量 3设置月初需量 4月初需量恢复',
    `time`           int(10) unsigned NOT NULL COMMENT '发生时间',
    `actual_demand` double unsigned NOT NULL,
    `create_by`      varchar(20)  DEFAULT NULL COMMENT '创建者',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;;

ALTER TABLE t_group
    ADD `demand_income` tinyint(1) NOT NULL DEFAULT '1' COMMENT '需量收益(false关闭)(true打开）';
ALTER TABLE t_group
    ADD `demand_calc_model` int(10)  NOT NULL DEFAULT '1' COMMENT '需量计算模式:1固定时段15分钟;2固定时段30分钟;3一分钟滑窗';
ALTER TABLE t_group
    ADD `demand_alarm_threshold` double NOT NULL DEFAULT '100' COMMENT '需量告警阈值(超出多少就告警)';
ALTER TABLE t_group
    ADD `capacity_controller` tinyint(1) NOT NULL DEFAULT '1' COMMENT '容量控制开关(false关闭)(true打开）';
ALTER TABLE t_strategy
    ADD `month_control_power` double NULL COMMENT '月初需量';
ALTER TABLE t_strategy
    ADD `grid_control_power` double NULL COMMENT '需量控制功率';
ALTER TABLE t_strategy_history
    ADD `month_control_power` double NULL COMMENT '月初需量';
ALTER TABLE t_strategy_history
    ADD `grid_control_power` double NULL COMMENT '需量控制功率';

###lindrom
CREATE TABLE control_15m
(
    projectId VARCHAR TAG,
    groupId   VARCHAR TAG,
    time      TIMESTAMP,
    control_power DOUBLE,
    meter_power DOUBLE,
    original_Demand DOUBLE
);
CREATE TABLE control_30m
(
    projectId VARCHAR TAG,
    groupId   VARCHAR TAG,
    time      TIMESTAMP,
    control_power DOUBLE,
    meter_power DOUBLE,
    original_Demand DOUBLE
);
CREATE TABLE control_15m_sliding
(
    projectId VARCHAR TAG,
    groupId   VARCHAR TAG,
    time      TIMESTAMP,
    control_power DOUBLE,
    meter_power DOUBLE,
    original_Demand DOUBLE
);
CREATE TABLE control_30m_sliding
(
    projectId VARCHAR TAG,
    groupId   VARCHAR TAG,
    time      TIMESTAMP,
    control_power DOUBLE,
    meter_power DOUBLE,
    original_Demand DOUBLE
);

###lindrom

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
         (127,	12,	'/system/alarm',	'声控报警'),
         (25,	NULL,	'/demand',	'需量通知'),
         (251,	25,	'/demand/email/add',	'增加通知'),
         (252,	25,	'/demand/email/edit',	'修改通知'),
         (253,	25,	'/demand/email/delete',	'删除通知'),
         (254,	25,	'/demand/email/query',	'查询通知'),
         (255,	25,	'/demand/job/manage',	'需量任务管理'),
         (256,	25,	'/demand/log/query',	'查询日志'),
         (207,	10,	'/diagram/getVoltageDiffRate',	'最大电压差'),
         (208,	10,	'/diagram/getTemperatureDiffRate',	'最大温度差');

CREATE TABLE `t_demand_email`
(
    `id`          varchar(128) NOT NULL COMMENT '通知识符uuid',
    `email`       varchar(32)  DEFAULT NULL COMMENT '邮箱',
    `project_id`  varchar(128) DEFAULT NULL COMMENT '项目id',
    `create_by`   varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE t_project
    ADD `whether_alarm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否报警';
ALTER TABLE t_project
    ADD `order` int NOT NULL DEFAULT '9' COMMENT '项目排序';
ALTER TABLE t_group
    ALTER COLUMN demand_controller SET DEFAULT '0';

【
1.3.5
】
ALTER TABLE t_group
    add `controllable_strategies` text COMMENT '项目排序';
ALTER TABLE t_controllable
    add `config` text COMMENT '配置';

update `t_group`
set `demand_income` = true
where `whether_system` = '1'
  and id in (select temp.id
             from (SELECT id
                   FROM `t_group`
                   WHERE `whether_system` = '1'
                     AND `demand_controller` = true) temp)

【
1.0.5
】
# 余热发电
ALTER TABLE t_group
    ADD `enable_waste_power_generation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否余热发电';
ALTER TABLE t_group
    ADD `group_earnings_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启分组收益开关';


【
1.3.5-2
】
# 2024-03-15 11:23:26 新增离网soc下限字段
ALTER TABLE t_strategy
    ADD `off_gird_soc` DOUBLE DEFAULT 5.0  COMMENT '离网soc下限';
ALTER TABLE t_strategy_history
    ADD `off_gird_soc` DOUBLE DEFAULT 5.0  COMMENT '离网soc下限';


INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (712, 7, '/monitor/steerable', '可控设备监控');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (1002, 1, '/homePage/hookup', '接线图密码');

ALTER TABLE t_user
    ADD `lock_count` int DEFAULT '0' COMMENT '是否账号锁定中';
#
初始化设置所有的账号 锁定状态为0
update t_user
set lock_count = 0;

#
2024-03-22 10:11:35 新增 国家id 暂时只在注册的时候记录下
ALTER TABLE t_user
    ADD `country_id` int DEFAULT NULL COMMENT '国家id';

#
ems soc上限策略
ALTER TABLE t_group
    add `ems_strategies` text COMMENT 'soc上限';

#
add email log
CREATE TABLE `t_email_log`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `group_id`    varchar(128) DEFAULT NULL COMMENT '分组id',
    `project_id`  varchar(128)  NOT NULL COMMENT '项目id',
    `email`       varchar(2500) NOT NULL COMMENT '收件人',
    `subject`     varchar(500)  NOT NULL COMMENT '主题',
    `content`     text          NOT NULL,
    `type`        int(1) NOT NULL COMMENT '1需量超了 2月初需量设置错误  3收益月报 4收益年报',
    `retry`       int(1) DEFAULT NULL COMMENT '补发',
    `success`     tinyint(1) NOT NULL COMMENT 'true发送成功 false发送失败',
    `time`        int(15) unsigned NOT NULL COMMENT '发生时间',
    `fix_time`    int(15) unsigned DEFAULT NULL COMMENT '修复时间',
    `create_by`   varchar(20)  DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

【demand
】
ALTER TABLE t_group
    ADD `demand_control_rate` double NOT NULL DEFAULT '100' COMMENT '需量控制系数(0-200)';

【
1.3.6
】
# 风电收益开关
ALTER TABLE t_group
    add `wind_earnings_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT '风电收益开关:true 是,false否';

#缓存
pvWind收益
CREATE TABLE `t_pv_wind_profit`
(
    `id`              int(32) NOT NULL AUTO_INCREMENT,
    `project_id`      varchar(128) NOT NULL COMMENT '项目id',
    `time`            bigint(20) NOT NULL COMMENT '数据时间',
    `year`            int(4) NOT NULL COMMENT '年',
    `month`           int(2) NOT NULL COMMENT '月',
    `day`             int(2) NOT NULL COMMENT '日',
    `pv_or_wind_type` varchar(128) NOT NULL COMMENT '区分pv的还是风电的',
    `peak_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '尖峰放电量',
    `vally_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '谷段放电量',
    `flat_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '平段放电量',
    `tip_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '尖峰放电量',
    `deep_vally_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '深谷放电量',

    `peak_discharge_quantity_online` double (30,15) DEFAULT NULL COMMENT '尖峰上网放电量',
    `vally_discharge_quantity_online` double (30,15) DEFAULT NULL COMMENT '谷段上网放电量',
    `flat_discharge_quantity_online` double (30,15) DEFAULT NULL COMMENT '平段上网放电量',
    `tip_discharge_quantity_online` double (30,15) DEFAULT NULL COMMENT '尖峰上网放电量',
    `deep_vally_discharge_quantity_online` double (30,15) DEFAULT NULL COMMENT '深谷上网放电量',

    `peak_discharge_quantity_dcdc` double (30,15) DEFAULT NULL COMMENT '尖峰放电量',
    `vally_discharge_quantity_dcdc` double (30,15) DEFAULT NULL COMMENT '谷段放电量',
    `flat_discharge_quantity_dcdc` double (30,15) DEFAULT NULL COMMENT '平段放电量',
    `tip_discharge_quantity_dcdc` double (30,15) DEFAULT NULL COMMENT '尖峰放电量',
    `deep_vally_discharge_quantity_dcdc` double (30,15) DEFAULT NULL COMMENT '深谷放电量',

    `peak_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '峰段收益',
    `vally_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '谷段收益',
    `flat_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '平段收益',
    `tip_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '尖峰收益',
    `deep_vally_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '深谷收益',

    `peak_sell_price` double (30,15) DEFAULT NULL COMMENT '峰价格',
    `vally_sell_price` double (30,15) DEFAULT NULL COMMENT '谷价格',
    `flat_sell_price` double (30,15) DEFAULT NULL COMMENT '平价格',
    `tip_sell_price` double (30,15) DEFAULT NULL COMMENT '尖价格',
    `deep_vally_sell_price` double (30,15) DEFAULT NULL COMMENT '深谷价格',

    `total_discharge_quantity` double (30,15) DEFAULT NULL COMMENT '总pv放电量',
    `total_discharge_self_quantity` double (30,15) DEFAULT NULL COMMENT '总自用电放电量',
    `total_full_internet_access_benefit` double (30,15) DEFAULT NULL COMMENT '全额上网计算方式的 总放点收益',
    `total_discharge_benefit` double (30,15) DEFAULT NULL COMMENT '自发自用计算方式的 总放电收益',
    `total_online_benefit` double (30,15) DEFAULT NULL COMMENT '上网收益',
    `total_online_quantity` double (30,15) DEFAULT NULL COMMENT '上网总电量',

    PRIMARY KEY (`project_id`, `pv_or_wind_type`, `time`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

#add
avc
ALTER TABLE t_group
    ADD `avc_monitor_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启了avc调度监控';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (1003, 1, '/homePage/avc', 'avc调度监控');

CREATE TABLE `t_price_template`
(
    `id`            BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `template_name` varchar(32)  DEFAULT NULL COMMENT '模版名称',
    `project_id`    varchar(128) DEFAULT NULL COMMENT '项目id',
    `demand_price` double DEFAULT NULL COMMENT '需量价格',
    `pv_self_price` double DEFAULT NULL COMMENT '光伏自用价格',
    `pv_df_price` double DEFAULT NULL COMMENT '光伏脱硫标杆价格',
    `pv_subsidy_price` double DEFAULT NULL COMMENT '光伏国家补贴价格',
    `pv_price` double DEFAULT NULL COMMENT '光伏发电价格',
    `wind_self_price` double DEFAULT NULL COMMENT '风电自用价格',
    `wind_df_price` double DEFAULT NULL COMMENT '风电脱硫标杆价格',
    `wind_subsidy_price` double DEFAULT NULL COMMENT '风电国家补贴价格',
    `wind_price` double DEFAULT NULL COMMENT '风电发电价格',
    `create_by`     varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time`   bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`     varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time`   bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


#把以前的price
变成 卖出价格
ALTER TABLE t_electric_price
    CHANGE price sell_price DOUBLE;

#新增买入价格
ALTER TABLE t_electric_price
    ADD `buy_price` double NULL  COMMENT '买入价格';

#update
t_electric_price set sell_price = price

#把买入价格变成和卖出价格一样
update t_electric_price
set buy_price = sell_price #电收益的 表的修改
ALTER TABLE t_electric
    CHANGE peak_price peak_sell_price DOUBLE;

ALTER TABLE t_electric
    CHANGE vally_price vally_sell_price DOUBLE;

ALTER TABLE t_electric
    CHANGE flat_price flat_sell_price DOUBLE;

ALTER TABLE t_electric
    CHANGE tip_price tip_sell_price DOUBLE;

ALTER TABLE t_electric
    CHANGE deep_vally_price deep_vally_sell_price DOUBLE;

ALTER TABLE t_electric
    ADD `peak_buy_price` double NULL  COMMENT '峰买入价格';
ALTER TABLE t_electric
    ADD `vally_buy_price` double NULL  COMMENT '谷买入价格';
ALTER TABLE t_electric
    ADD `flat_buy_price` double NULL  COMMENT '平买入价格';
ALTER TABLE t_electric
    ADD `tip_buy_price` double NULL  COMMENT '尖买入价格';
ALTER TABLE t_electric
    ADD `deep_vally_buy_price` double NULL  COMMENT '深谷买入价格';

update t_electric
set peak_buy_price = peak_sell_price;
update t_electric
set vally_buy_price = vally_sell_price;
update t_electric
set flat_buy_price = flat_sell_price;
update t_electric
set tip_buy_price = tip_sell_price;
update t_electric
set deep_vally_buy_price = deep_vally_sell_price;

#最大功率曲线
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
             (209,	10,	'/diagram/getMaxDemandRate',	'需量曲线查询');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (210, 10, '/diagram/getGirdSupplyPoint', '并网点功率曲线查询');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (60009, 6, '/heatMap/pvWasterDischarge', '余热发电热力图');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (211, 10, '/diagram/selfWasterDefine', '余热发电曲线查询');
#电价模版权限配置
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
             (147,	14,	'/manage/priceTemplate/update',	'电价模版保存更新');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (148, 14, '/manage/priceTemplate/get', '电价模版查询');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (149, 14, '/manage/priceTemplate/delete', '电价模版删除');
#项目备注和用户的绑定关联表
CREATE TABLE `t_project_user_remark`
(
    `id`          BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id`  varchar(128) DEFAULT NULL COMMENT '项目id',
    `user_id`     varchar(128) DEFAULT NULL COMMENT '用户id',
    `remark`      text         DEFAULT NULL COMMENT '备注',
    `role`        varchar(128) DEFAULT NULL COMMENT '角色',
    `user_name`   varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_by`   varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (54, 5, '/operation/updateGroupEarnings', '修改分组储能开关');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (60010, 24, '/cloudLargescreenDisplay', '大屏显示');

ALTER TABLE t_electric_price
    ADD `price_template_id` bigint(20) DEFAULT NULL COMMENT '电价模版id';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (55, 5, '/operation/priceTemplate/update', '保存或更新电价配置模版(项目)');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (56, 5, '/operation/priceTemplate/delete', '电价配置模版(项目)');

--- 1.3.7
-- 添加从机号到相关表
ALTER TABLE t_ammeter
    ADD `slave_id` int(10) NOT NULL DEFAULT '1' COMMENT '从机号 默认 1';
ALTER TABLE t_controllable
    ADD `slave_id` int(10) NOT NULL DEFAULT '1' COMMENT '从机号 默认 1';
-- 模版添加数据中心字段筛选
alter table `t_price_template`
    add `price_type` ENUM('PEAK_VALLEYS_PRICE_PERIOD', 'FIXED_PRICE_PERIOD', 'DYNAMIC_PRICE_PERIOD') NOT NULL DEFAULT 'PEAK_VALLEYS_PRICE_PERIOD'  COMMENT '电价类型';


alter table `t_electric_price`
    add `on_line_price` DOUBLE DEFAULT NULL COMMENT '上网电价(馈网电价)';
alter table `t_electric_price`
    add `type` varchar(50) DEFAULT NULL COMMENT '类型区分是 海外的还是国内的';
alter table `t_electric_price`
    add `custom_period_name` varchar(100) DEFAULT NULL COMMENT '自定义的时段的名称';


-- 电价支持欧州的 需求
ALTER TABLE t_project
    ADD `electric_price_type` VARCHAR(100) DEFAULT 'PEAK_VALLEYS_PRICE_PERIOD' COMMENT '电价类型字段',
    ADD `electric_price_area` VARCHAR(100) DEFAULT NULL COMMENT '电价区域',
    ADD `electric_price_span` VARCHAR(100) DEFAULT NULL COMMENT '电价时段';
-- 把项目的电价类型全部设置成 尖峰平谷类型
UPDATE t_project
SET electric_price_type = 'PEAK_VALLEYS_PRICE_PERIOD';
UPDATE t_electric_price
SET type = 'PEAK_VALLEYS_PRICE_PERIOD'
WHERE pid IS NULL;

-- ----------------------------
-- Table structure for t_electric
-- ----------------------------
CREATE TABLE `t_electric_dynamic_period`
(
    `id`                int(32) NOT NULL AUTO_INCREMENT,
    `project_id`        varchar(128) NOT NULL COMMENT '项目id',
    `device_id`         varchar(128) NOT NULL COMMENT '设备Id',
    `period_start_time` bigint(20) NOT NULL COMMENT '数据段开始时间',
    `period_end_time`   bigint(20) NOT NULL COMMENT '数据段结束时间',
    `year`              int(4) NOT NULL COMMENT '年',
    `month`             int(2) NOT NULL COMMENT '月',
    `day`               int(2) NOT NULL COMMENT '日',
    `charge_quantity` double (30,15) DEFAULT NULL COMMENT '充电量',
    `charge_cost` double (30,15) DEFAULT NULL COMMENT '充电成本',
    `discharge_quantity` double (30,15) DEFAULT NULL COMMENT '放电量',
    `discharge_benefit` double (30,15) DEFAULT NULL COMMENT '放电收益',
    `sell_price` double DEFAULT NULL,
    `buy_price` double DEFAULT NULL,
    `total_benefit` double (30,15) DEFAULT NULL COMMENT '总收益(减去成本的)',
    PRIMARY KEY (`project_id`, `device_id`, `period_start_time`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4307 DEFAULT CHARSET=utf8;



CREATE TABLE `t_pv_wind_dynamic_profit`
(
    `id`                int(32) NOT NULL AUTO_INCREMENT,
    `project_id`        varchar(128) NOT NULL COMMENT '项目id',
    `period_start_time` bigint(20) NOT NULL COMMENT '数据段开始时间',
    `period_end_time`   bigint(20) NOT NULL COMMENT '数据段结束时间',
    `year`              int(4) NOT NULL COMMENT '年',
    `month`             int(2) NOT NULL COMMENT '月',
    `day`               int(2) NOT NULL COMMENT '日',
    `pv_or_wind_type`   varchar(128) NOT NULL COMMENT 'pv或者是wind',
    `discharge_quantity` double (30,15) DEFAULT NULL COMMENT '放电量(自用电)',
    `discharge_quantity_online` double (30,15) DEFAULT NULL COMMENT '上网电量',
    `discharge_quantity_dcdc` double (30,15) DEFAULT NULL COMMENT 'dcdc电量',
    `discharge_benefit` double (30,15) DEFAULT NULL COMMENT '放电收益',
    `total_full_internet_access_benefit` double (30,15) DEFAULT NULL COMMENT '全额上网模式收益',
    `agreement_benefit` double (30,15) DEFAULT NULL COMMENT '时段的协议模式收益(需要加上online收益)',
    `total_online_benefit` double (30,15) DEFAULT NULL COMMENT '上网的总收益',
    `total_discharge_quantity` double (30,15) DEFAULT NULL COMMENT 'pv的总电量',
    PRIMARY KEY (`project_id`, `pv_or_wind_type`, `period_start_time`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4307 DEFAULT CHARSET=utf8;


CREATE TABLE `t_income_divide_into`
(
    `id`               int(32) NOT NULL AUTO_INCREMENT,
    `project_id`       varchar(128) NOT NULL COMMENT '项目id',
    `name_of_employer` varchar(128) NOT NULL COMMENT '资方单位名称',
    `name_of_customer` varchar(128) NOT NULL COMMENT '客户单位名称',
    `customer_occupancy` double (30,15) DEFAULT NULL COMMENT '客户投资方占比',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

-- 系统配图 (0:储能柜 1:集装箱)
ALTER TABLE t_group
    add `diagram_of_system` ENUM('0', '1') NOT NULL DEFAULT '0' COMMENT '首页系统配图(0:储能柜 1:集装箱)';


ALTER TABLE t_price_area
    add `price_type` ENUM('PEAK_VALLEYS_PRICE_PERIOD', 'FIXED_PRICE_PERIOD', 'DYNAMIC_PRICE_PERIOD') NOT NULL DEFAULT 'PEAK_VALLEYS_PRICE_PERIOD'  COMMENT '电价类型';

ALTER TABLE t_price_area
    add `country_id` int(10) DEFAULT NULL COMMENT '国家id';

ALTER TABLE t_price_area
    add `electric_price_area` varchar(100) DEFAULT NULL COMMENT '电价区域';

ALTER TABLE t_price_area
    add `electric_price_span` varchar(100) DEFAULT NULL COMMENT '电价时段';


-- 添加一个 标记是否是数据校准的字段
ALTER TABLE t_pv_wind_profit
    ADD COLUMN data_calibration_flag boolean default false;

-- 添加一个 数据校准的操作人
ALTER TABLE t_pv_wind_profit
    ADD COLUMN user_id varchar(100) DEFAULT NULL COMMENT '数据校准的操作人';

-- 添加一个 数据校准的操作人
ALTER TABLE t_electric
    ADD COLUMN user_id varchar(100) DEFAULT NULL COMMENT '数据校准的操作人';

-- 把原本的 主键约束先删除
ALTER TABLE t_pv_wind_profit
drop
primary key

-- 添加新的主键约束 多了一个 数据校准字段
ALTER TABLE t_pv_wind_profit
    add PRIMARY KEY (project_id, pv_or_wind_type, time, data_calibration_flag);


ALTER TABLE t_pv_wind_profit
    ADD COLUMN total_agreement_benefit double(30,15) DEFAULT NULL COMMENT '协议模式的收益(需要加上online收益)';

ALTER TABLE t_project
    ADD COLUMN time_sharing_cache boolean DEFAULT false COMMENT '分时缓存开关';

-- 系统配置和其他表的扩展
ALTER TABLE t_event_code
    ADD `permission` SMALLINT(6) DEFAULT NULL COMMENT '事件屏蔽级别', ADD `event_code` VARCHAR(128) COMMENT '事件编码';
ALTER TABLE t_meter_event_code
    ADD `event_code` VARCHAR(128) COMMENT '事件编码';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (83, 8, '/event/showHide', '查看隐藏事件');

alter table `t_ammeter`
    add `dlt645_device_address` varchar(128) DEFAULT NULL COMMENT 'dlt645设备地址';
alter table `t_ammeter`
    add `dlt645_prefix_cmd` varchar(128) DEFAULT NULL COMMENT 'dlt645前置cmd';
ALTER TABLE t_project
    ADD `non_hide_fault` int(10) NOT NULL DEFAULT '0' COMMENT '屏蔽后的故障数';
ALTER TABLE t_project
    ADD `non_hide_alarm` int(10) NOT NULL DEFAULT '0' COMMENT '屏蔽后的告警数';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (84, 8, '/event/updateBatch', '批量确认');

ALTER TABLE t_controllable
    add `design_power` double COMMENT '设计功率';
ALTER TABLE t_controllable
    add `listen_port` int(10) COMMENT '监听端口';
ALTER TABLE t_controllable
    add `power_step_percentage` double NOT NULL DEFAULT '0.3' COMMENT '功率斜率';

CREATE TABLE `t_investor`
(
    `id`          varchar(100) NOT NULL COMMENT '资方 id',
    `name`        varchar(128) NOT NULL COMMENT '资方名称',
    `address`     varchar(1280) DEFAULT NULL COMMENT '资方地址',
    `create_by`   varchar(32)   DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(32)   DEFAULT NULL COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='投资方';


CREATE TABLE `t_investor_project`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '关系id',
    `investor_id` varchar(128) NOT NULL COMMENT '资方id',
    `project_id`  varchar(128) NOT NULL COMMENT '项目id',
    `create_by`   varchar(32) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(32) DEFAULT NULL COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `t_investor_project_pk` (`investor_id`,`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资方与项目关系';

ALTER TABLE t_project
    ADD `order_shield` int NOT NULL DEFAULT '99' COMMENT '屏蔽项目排序';
ALTER TABLE t_ammeter
    ADD `time_sharing_measurement` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分时计量';
ALTER TABLE t_ammeter
    ADD `max_demand_measurement` tinyint(1) NOT NULL DEFAULT '0' COMMENT '最大需量计量';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (212, 10, '/diagram/getMaxDemandRateFromMeter', '电表需量曲线查询');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9001, 9, '/report/readingMeter/month', '电表抄表月报');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9002, 9, '/report/readingMeter/year', '电表抄表年报');

ALTER TABLE t_strategy
    ADD `price_difference` double COMMENT '电价最大差价';
ALTER TABLE t_strategy
    ADD `price_benchmark` double COMMENT '电价定值';
ALTER TABLE t_strategy
    ADD `strategy_type` tinyint(1)  DEFAULT '1' COMMENT '策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式';
ALTER TABLE t_strategy_history
    ADD `price_difference` double COMMENT '电价最大差价';
ALTER TABLE t_strategy_history
    ADD `price_benchmark` double COMMENT '电价定值';
ALTER TABLE t_strategy_history
    ADD `strategy_type` tinyint(1) DEFAULT '1' COMMENT '策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式';

CREATE TABLE `t_strategy_upload_log`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id`  varchar(128) NOT NULL COMMENT '项目id',
    `type`        int(1) NOT NULL COMMENT ' 下发策略的场景',
    `retry`       int(1) DEFAULT NULL COMMENT '重试次数',
    `success`     tinyint(1) NOT NULL COMMENT 'true发送成功 false发送失败',
    `time`        bigint(20) unsigned NOT NULL COMMENT '发生时间',
    `fix_time`    bigint(20) unsigned DEFAULT NULL COMMENT '修复时间',
    `create_by`   tinytext COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   tinytext COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (26, NULL, '/manage/investor', '资方管理'),
       (261, 26, '/manage/investor/add', '资方增加'),
       (262, 26, '/manage/investor/delete', '资方删除'),
       (263, 26, '/manage/investor/edit', '资方修改'),
       (264, 26, '/manage/investor/list', '资方列表查询');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (155, 15, '/manage/project/showInvestor', '资方管理');

CREATE TABLE `t_hide_event_code`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `event_code`  varchar(20)  DEFAULT NULL COMMENT '备注',
    `type`        varchar(10)  DEFAULT NULL,
    `project_id`  varchar(128) DEFAULT NULL,
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`   varchar(32)  DEFAULT NULL COMMENT '创建者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`   varchar(32)  DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='告警信息表';

ALTER TABLE t_electric
    ADD `calibration_type` tinyint(1) DEFAULT '1' NOT NULL COMMENT '校准类型，默认 1原始值  2校准后失效 3校准后新增';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (85, 8, '/event/showAlarmEvent', '查看告警事件');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (57, 5, '/notice/incomeDivideInto/add', '收益分成设置');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (126, 12, '/system/reRun', '重跑页面');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (129, 12, '/system/operateHideCode', '新增隐藏code');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (128, 12, '/system/showHideCode', '查看隐藏code');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (27, NULL, '/manage/function', '管理端功能配置');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (271, 27, '/manage/function/reRun', '重跑页面');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (114, 10, '/diagram/getApparentPowerRateByCustom', '自定义曲线电网视在功率');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (69, 5, '/operation/getRealTimePriceDiagram', '项目端电价曲线接口');

###lindrom
CREATE TABLE capacity_1d
(
    projectId VARCHAR TAG,
    groupId   VARCHAR TAG,
    time      TIMESTAMP,
    control_power DOUBLE
);


###增加country
增加2个字段
###详细见1.3.7-t_country

###1.3.8 项目增加hash字段
ALTER TABLE t_project
    ADD `hash` int(10) NULL COMMENT '项目hash值';

---###1.3.9
CREATE TABLE t_retry_fail_task
(
    id            INT AUTO_INCREMENT PRIMARY KEY,
    task_id       VARCHAR(255) NOT NULL COMMENT '任务id',
    task_label    VARCHAR(255) NOT NULL COMMENT '任务标签',
    project_id    VARCHAR(255) NOT NULL COMMENT '项目id',
    state         BOOLEAN      NOT NULL COMMENT '状态, true 成功， false失败',
    retry_count   BIGINT       NOT NULL COMMENT '目前重试次数',
    fail_msg      TEXT COMMENT '失败信息',
    context_json  TEXT COMMENT '上下文json , 这个是根据需要的业务进行保存的',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`   varchar(32) DEFAULT NULL COMMENT '创建者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`   varchar(32) DEFAULT NULL COMMENT '更新者'
) COMMENT='retry失败的任务记录';

CREATE TABLE t_group_demand_month_income
(
    id                 INT AUTO_INCREMENT PRIMARY KEY,
    group_id           VARCHAR(255) NOT NULL COMMENT '分组id',
    project_id         VARCHAR(255) NOT NULL COMMENT '项目id',
    demand_max_power DOUBLE COMMENT '最大需量月度',
    demand_max_power_meter DOUBLE COMMENT '最大需量月度(计量电表)',
    demand_income_power DOUBLE COMMENT '计算需量收益的(降低需量功率)',
    `month_start_time` bigint(20) DEFAULT NULL COMMENT '月度开始时间',
    `month_end_time`   bigint(20) DEFAULT NULL COMMENT '月度结束时间',
    `create_time`      bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`        varchar(32) DEFAULT NULL COMMENT '创建者',
    `update_time`      bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`        varchar(32) DEFAULT NULL COMMENT '更新者',
    UNIQUE INDEX idx_unique_project_group_month (project_id, group_id, month_start_time)
) COMMENT='分组需量月度收益';

ALTER TABLE t_group
    add `demand_control` ENUM('disable', 'enable_show_demand_income', 'enable_hide_demand_income') NOT NULL DEFAULT 'enable_show_demand_income'  COMMENT '"控制需量整合下拉框(不启用disable, 启用,显示需量收益enable_show_demand_income, 启用,隐藏需量收益enable_hide_demand_income )';

ALTER TABLE t_group
    add `demand_control_adjust_model` ENUM('manual', 'auto_calc', 'auto_meter') NOT NULL DEFAULT 'manual'  COMMENT '当前控制需量调整模式 , manual, auto_calc, auto_meter';

ALTER TABLE t_group
    ADD `slip_time` int(10) DEFAULT 1 COMMENT '滑差';

ALTER TABLE t_group
    ADD `demand_period` int(10) DEFAULT NULL COMMENT '周期';

ALTER TABLE t_group
    ADD `demand_control_auto_rate` double DEFAULT 99.6 COMMENT '需量自动调整比例(%) , 自动抬升的时候需要';

ALTER TABLE t_group
    ADD `demand_control_auto_up_limit` double DEFAULT NULL COMMENT '需量自动调整上线(kW)';
ALTER TABLE t_group
    ADD `demand_remind_controller` boolean DEFAULT true COMMENT '需量提醒开关';

ALTER TABLE t_project
    MODIFY COLUMN `whether_backup` ENUM('on_grid_run', 'off_grid_run', 'on_off_grid_run_manual', 'on_off_grid_run_auto') NOT NULL DEFAULT 'on_grid_run' COMMENT '并离网切换 , 并网运行(默认), 离网运行, 并离网运行(手动), 并离网运行(自动)';
update t_project
set whether_backup = 'on_grid_run';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (80, 7, '/monitor/canControllableAction', '可控设备控制操作');

-- 负荷配图
ALTER TABLE t_group
    add `diagram_of_load` ENUM('common', 'charging_piles') NOT NULL DEFAULT 'common' COMMENT '负荷配图(common:通用 charging_piles:充电桩)';



CREATE TABLE `t_point_data_200`
(
    `point_id`      int(11) NOT NULL AUTO_INCREMENT,
    `point_offset`  tinytext COMMENT '偏移量',
    `point_address` int(11) DEFAULT NULL COMMENT '地址',
    `point_name`    tinytext COMMENT '点位名',
    `point_column`  tinytext COMMENT '字段名',
    `point_mul`     int(11) DEFAULT NULL COMMENT '数值倍率',
    `point_type`    varchar(10) NOT NULL COMMENT '数据类型',
    `point_level`   varchar(5)  DEFAULT NULL COMMENT '采样等级',
    `point_len`     tinytext COMMENT '单个采样长度',
    `create_time`   bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`     varchar(32) DEFAULT NULL COMMENT '创建者',
    `update_time`   bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`     varchar(32) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`point_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='点位数据对象';

ALTER TABLE t_group
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_ammeter
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_device
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_camera
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_controllable
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_ammeter
    ADD `frequency_regulation` varchar(20) NOT NULL DEFAULT 'nil' COMMENT '调频';

ALTER TABLE t_project
    ADD `order_rule` int NOT NULL DEFAULT '0' COMMENT '项目排序';
ALTER TABLE t_project
    ADD `order_one` int NOT NULL DEFAULT '9' COMMENT '一类排序';
ALTER TABLE t_project
    ADD `order_one_shield` int NOT NULL DEFAULT '9' COMMENT '一类告警排序';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (156, 15, '/manage/project/orderRule', '排序规则 默认 0，展示用 1');

ALTER TABLE t_group
    add `open_vpp` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启vpp: true 是,false否';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (86, 8, '/event/waveRecord', '录波事件');


-- 更新 t_project_ext 表中的 area 字段，使用 t_price_area 表中对应的 id
UPDATE t_project_ext p
    JOIN t_price_area pa
ON p.area = pa.area
    SET p.area = pa.id;

-- 对于无法匹配到的区域，输出日志
SELECT p.id, p.area
FROM t_project_ext p
         LEFT JOIN t_price_area pa ON p.area = pa.area
WHERE pa.id IS NULL;


-- 更新 t_group 表中的 demand_calc_model, demand_period, 和 slip_time 字段
UPDATE t_group
SET demand_calc_model = CASE
                            WHEN demand_calc_model = 1 THEN 1 -- FIXED 模式的值
                            WHEN demand_calc_model = 2 THEN 1
                            WHEN demand_calc_model = 3 THEN 2 -- SLIDING 模式的值
                            WHEN demand_calc_model = 4 THEN 2
                            ELSE demand_calc_model
    END,
    demand_period     = CASE
                            WHEN demand_calc_model = 1 THEN 15
                            WHEN demand_calc_model = 2 THEN 30
                            WHEN demand_calc_model = 3 THEN 15
                            WHEN demand_calc_model = 4 THEN 30
                            ELSE demand_period
        END,
    slip_time         = CASE
                            WHEN demand_calc_model = 3 THEN 1
                            WHEN demand_calc_model = 4 THEN 1
                            ELSE slip_time
        END
WHERE demand_calc_model IN (1, 2, 3, 4);

-- 更新 t_group 表中的 demand_control 字段
UPDATE t_group
SET demand_control = CASE
                         WHEN demand_controller = 1
                             THEN 'enable_show_demand_income' -- 对应 DemandControlEnum.enable_show_demand_income
                         ELSE 'disable' -- 对应 DemandControlEnum.disable
    END
WHERE demand_controller IS NOT NULL;

CREATE TABLE `t_meter_data_remedies`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `project_id`  varchar(128)         DEFAULT NULL COMMENT '项目id',
    `meter_id`    varchar(128)         DEFAULT NULL COMMENT '电表 id',
    `time`        bigint(20) NOT NULL COMMENT '数据时间',
    `day`         bigint(20) NOT NULL COMMENT '日期',
    `report_type` varchar(10) NOT NULL DEFAULT '0' COMMENT '日报类型',
    `data_type`   varchar(10) NOT NULL COMMENT '数据类型',
    `data` double NOT NULL DEFAULT '0' COMMENT '数据',
    `create_by`   tinytext COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   tinytext COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据状态';

CREATE TABLE `t_report_data_remedies`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `project_id`  varchar(128)         DEFAULT NULL COMMENT '项目id',
    `time`        bigint(20) NOT NULL COMMENT '数据时间',
    `day`         bigint(20) NOT NULL COMMENT '日期',
    `report_type` varchar(10) NOT NULL DEFAULT '0' COMMENT '日报类型',
    `data_type`   varchar(10) NOT NULL COMMENT '数据类型',
    `data` double NOT NULL DEFAULT '0' COMMENT '数据',
    `create_by`   tinytext COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   tinytext COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据状态';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9003, 9, '/report/remedies', '电站补值');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9004, 9, '/report/meter/remedies', '电表补值');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9005, 9, '/report/remedies/query', '补值查询');

ALTER TABLE t_group
    ADD `wave_record` boolean DEFAULT false COMMENT '开启录波';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (1105, 10, '/diagram/getMeterDemand', '电表需量曲线');

CREATE TABLE `t_event_code_language`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT,
    `point_column`     varchar(50)  DEFAULT NULL COMMENT '对应的点位字段',
    `device_type_code` int(11) DEFAULT NULL COMMENT '设备类型码',
    `device_type`      varchar(50)  DEFAULT NULL COMMENT '设备类型名称',
    `event_level`      varchar(20)  DEFAULT NULL COMMENT '事件等级Fault、State、Alarm',
    `bit_offset`       int(11) DEFAULT NULL COMMENT 'BIT码偏移量',
    `bit_value`        int(11) DEFAULT NULL COMMENT 'BIT码',
    `zh_CN`            varchar(255) DEFAULT NULL COMMENT '中文',
    `en_US`            varchar(255) DEFAULT NULL COMMENT '英文',
    `de_DE`            varchar(255) DEFAULT NULL COMMENT '德语',
    `nl_NL`            varchar(255) DEFAULT NULL COMMENT '荷兰',
    `sv_SE`            varchar(255) DEFAULT NULL COMMENT '瑞典',
    `it_IT`            varchar(255) DEFAULT NULL COMMENT '意大利',
    `pl_PL`            varchar(255) DEFAULT NULL COMMENT '波兰',
    `bg_BG`            varchar(255) DEFAULT NULL COMMENT '保加利亚',
    `event_code`       varchar(128) DEFAULT NULL,
    `create_by`        varchar(20)  DEFAULT NULL COMMENT '创建者',
    `create_time`      bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`      bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='告警信息表';

ALTER TABLE t_event_message
    ADD `whether_hide` tinyint(1) NULL DEFAULT '0' COMMENT '是否隐藏';

ALTER TABLE t_event_message
    ADD `event_key` varchar(128) COMMENT '事件编码';

UPDATE t_event_message
SET create_time = CASE
                      WHEN create_time < 10000000000 THEN create_time * 1000
                      ELSE create_time
    END,
    update_time = CASE
                      WHEN update_time < 10000000000 THEN update_time * 1000
                      ELSE update_time
        END;



[1.4.1]
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (58, 5, '/operation/getPrice', '查询电价 ');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (59, 5, '/operation/savePrice', '保存电价');

ALTER TABLE t_project_ext
    ADD COLUMN hide_all_ems_kernel_code BOOLEAN NOT NULL DEFAULT 0 COMMENT '隐藏所有 ems 核心code';
ALTER TABLE t_project_ext
    ADD COLUMN hide_all_ems_sub_device_code BOOLEAN NOT NULL DEFAULT 0 COMMENT '隐藏所有 ems 子设备 code';
ALTER TABLE t_project_ext
    ADD COLUMN hide_all_meter_code BOOLEAN NOT NULL DEFAULT 0 COMMENT '隐藏 meter code';

update t_hide_event_code
set type = '1'
where event_code like '65534_%'
   or event_code like '65535_%';
update t_hide_event_code
set type = '2'
where type = 'EMS';
update t_hide_event_code
set type = '3'
where type = 'METER';

ALTER TABLE t_group
    ADD `agc_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'agc开关';

ALTER TABLE t_group
    ADD `avc_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'avc开关';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (60012, 10, '/diagram/getAgcRate', 'agc曲线'),
       (60013, 10, '/diagram/getAvcRate', 'avc曲线');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (60014, 14, '/manage/project/operation', '管理的项目收益');

ALTER TABLE t_device
    ADD COLUMN `is_host` TINYINT(1) DEFAULT 0 COMMENT '是否主机0否1是';


DROP PROCEDURE IF EXISTS handle_table;
DELIMITER
$$
CREATE PROCEDURE handle_table()
BEGIN
    IF
EXISTS (
        SELECT *
        FROM information_schema.tables
        WHERE table_name = 't_demand' AND table_schema = DATABASE()
    ) THEN
        RENAME TABLE t_demand TO t_demand_last_record;
END IF;
CREATE TABLE IF NOT EXISTS t_demand_last_record
(
    id
    INT
    NOT
    NULL
    AUTO_INCREMENT,
    time
    BIGINT
    NOT
    NULL,
    project_id
    VARCHAR
(
    255
) NOT NULL,
    group_id VARCHAR
(
    255
) NOT NULL,
    create_by VARCHAR
(
    255
),
    create_time BIGINT,
    update_by VARCHAR
(
    255
),
    update_time BIGINT,
    PRIMARY KEY
(
    id
),
    UNIQUE KEY unique_project_group
(
    project_id,
    group_id
) -- 添加联合唯一索引
    );
END$$
DELIMITER ;
CALL handle_table();


-- 1.4.2

ALTER TABLE t_log
    add `params` text COMMENT '参数';
ALTER TABLE t_log
    add `detail` text COMMENT '细节';
ALTER TABLE t_log
    ADD `client` varchar(20) NOT NULL DEFAULT 'PC' COMMENT '客户端';
ALTER TABLE t_log
    ADD `trace_id` varchar(256) NULL COMMENT '追踪标识id';

ALTER TABLE t_electric_price
    ADD `waster_self_price` double NULL  COMMENT '余热发电自用价格';
ALTER TABLE t_electric_price
    ADD `waster_df_price` double NULL  COMMENT '余热发电脱硫标杆价格';
ALTER TABLE t_electric_price
    ADD `waster_subsidy_price` double NULL  COMMENT '余热发电国家补贴价格';
ALTER TABLE t_electric_price
    ADD `waster_price` double NULL  COMMENT '余热发电发电价格';


ALTER TABLE t_price_template
    ADD `waster_self_price` double NULL  COMMENT '余热发电自用价格';
ALTER TABLE t_price_template
    ADD `waster_df_price` double NULL  COMMENT '余热发电脱硫标杆价格';
ALTER TABLE t_price_template
    ADD `waster_subsidy_price` double NULL  COMMENT '余热发电国家补贴价格';
ALTER TABLE t_price_template
    ADD `waster_price` double NULL  COMMENT '余热发电发电价格';


ALTER TABLE t_group
    ADD `waster_earnings_controller` tinyint(1) NOT NULL DEFAULT '0'  COMMENT '余热发电收益开关';
ALTER TABLE t_group
    ADD `waster_power_model` int(10) default 2 NOT NULL COMMENT '余热发电模式 1全额上网 2自发自用余量上网 3 自发自用(协议电价)';

ALTER TABLE t_pv_wind_profit CHANGE COLUMN pv_or_wind_type renewable_type VARCHAR (128);
RENAME
TABLE t_pv_wind_profit TO t_renewable_profit;

ALTER TABLE t_pv_wind_dynamic_profit CHANGE COLUMN pv_or_wind_type renewable_type VARCHAR (128);
RENAME
TABLE t_pv_wind_dynamic_profit TO t_renewable_dynamic_profit;


CREATE TABLE `t_day_report`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT,
    `project_id`     varchar(50) NOT NULL,
    `equipment_id`   varchar(50) NOT NULL,
    `equipment_type` varchar(50) NOT NULL,
    `time`           bigint(20)  NOT NULL COMMENT '数据时间',
    `in_data` double (30,15) DEFAULT 0 COMMENT '输入电量',
    `out_data` double (30,15) DEFAULT 0 COMMENT '输出电量',
    `out_ems_dcdc_data` double (30,15) DEFAULT 0 COMMENT 'ems内部dcdc输出电量',
    `create_by`      varchar(20) DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(20) DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`project_id`, `equipment_id`, `time`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='日报表缓存记录表';


ALTER table t_controllable
    add `connection_type` varchar(100) DEFAULT NULL COMMENT '连接方式';
update t_controllable
set connection_type= 'TCPCAN'
where vendor != 'HuaweiCharger';

ALTER table t_controllable
    add `interface_name` varchar(100) DEFAULT null COMMENT '串口名称';
ALTER table t_controllable
    add `bitrate` int(10) DEFAULT 0 COMMENT '波特率';


ALTER TABLE t_group
    ADD `capacity_alarm_threshold` double NOT NULL DEFAULT '100' COMMENT '容量告警阈值(超出多少就告警)';
ALTER TABLE t_group
    ADD `capacity_remind_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT '容量提醒开关(false关闭)(true打开）';

-- 1.4.2添加电表协议字段
alter table t_ammeter
    add protocol varchar(20) default 'TCP/IP' comment '协议';
alter table t_ammeter
    add device varchar(20) default null comment '设备名称';
alter table t_ammeter
    add baud_rate int default null comment '波特率';
alter table t_ammeter
    add data_bits int default null comment '数据位';
alter table t_ammeter
    add stop_bits int default null comment '停止位';
alter table t_ammeter
    add parity varchar(1) default null comment '奇偶校验';


CREATE TABLE `t_capacity_alarm_record`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `group_id`       varchar(128) NOT NULL COMMENT '分组id',
    `project_id`     varchar(128) NOT NULL COMMENT '项目id',
    `description`    varchar(256) DEFAULT NULL COMMENT '描述',
    `description_en` varchar(256) DEFAULT NULL COMMENT '英文描述',
    `type`           int(1) NOT NULL COMMENT '1 容量高级超了 0消失记录',
    `time`           int(10) unsigned NOT NULL COMMENT '时间',
    `alarm_value` double unsigned NOT NULL,
    `control_value` double unsigned NOT NULL,
    `create_by`      varchar(20)  DEFAULT NULL COMMENT '创建者',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- AI对话消息记录表
CREATE TABLE t_chat_session
(
    id          BIGINT PRIMARY KEY COMMENT '主键ID',
    chat_id     VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '对话ID',
    user_id     VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '用户ID',
    title       VARCHAR(255) NOT NULL DEFAULT '' COMMENT '会话标题',
    create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户会话表';

CREATE TABLE t_chat_session_message
(
    id                BIGINT PRIMARY KEY COMMENT '主键ID',
    session_id        BIGINT    NOT NULL DEFAULT 0 COMMENT '会话ID',
    type              INT       NOT NULL DEFAULT 0 COMMENT '角色',
    parent_id         BIGINT    NOT NULL COMMENT '父级消息Id',
    reasoning_time    DECIMAL NULL COMMENT '思考时间',
    reasoning_content TEXT NULL COMMENT '思考内容',
    content           TEXT      NOT NULL COMMENT '消息内容',
    create_time       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话消息历史';



INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (10111, 10, '/diagram/getBatteryRateWhoStation', '整站导出');

ALTER TABLE t_group
    ADD `mg_soc_balance` tinyint(1)  DEFAULT '0' COMMENT  '微网储能SOC被动均衡, 打开后，并网储能功率会根据离网储能的SOC和功率自动调节，达到所有储能SOC的平衡状态';

ALTER TABLE t_group
    ADD `mg_soc_balance_off_grid_power_limit_ratio` double DEFAULT 0.8 COMMENT  '微网储能SOC被动均衡功率比例 默认0.8';


INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (28, null, '/capacity', '容量管理');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (10222, 28, '/capacity/alarm/query', '容量告警记录查询');

-- 新增操作明细权限
INSERT INTO `t_authority` (`pid`, `ap_key`, `name`)
VALUES (16, '/manage/user/log/detail', '操作明细');

INSERT INTO `t_authority` (`pid`, `ap_key`, `name`)
VALUES (11, '/user/log/detail', '操作明细');

-- 电站控制策略增加并离网soc上限
ALTER TABLE t_strategy
    ADD `off_grid_soc_high_limit` double DEFAULT 100 COMMENT  '离网soc上限';

ALTER TABLE t_strategy
    ADD `charge_soc_high_limit` double DEFAULT 100 COMMENT  '并网soc上限';

ALTER TABLE t_group
    ADD `home_page_reactive_power_controller` tinyint(1)  NOT NULL DEFAULT '0' COMMENT '首页显示无功功率开关(默认关闭)';
ALTER TABLE t_group
    ADD `home_page_zero_power_controller` tinyint(1) NOT NULL DEFAULT '1' COMMENT '首页整站/单机零值开关(默认开启)'


-- 项目配置表
CREATE TABLE IF NOT EXISTS t_configuration
(
    id
    INT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    project_id
    VARCHAR
(
    128
) not null COMMENT '项目id',
    name VARCHAR
(
    255
) COMMENT '配置名称',
    type INT COMMENT '类型',
    password VARCHAR
(
    255
) COMMENT '密码',
    config_url VARCHAR
(
    255
) COMMENT '配置路径',
    generation_type INT COMMENT '生成类型',
    graph_script MEDIUMTEXT COMMENT '绘图脚本',
    is_released tinyint DEFAULT 0 COMMENT '是否发布',
    index_order INT COMMENT '排序',
    create_time timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置表';

-- 运维信息
CREATE TABLE IF NOT EXISTS t_maintenance
(
    id
    INT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    project_id
    VARCHAR
(
    128
) not null COMMENT '项目id',
    factory_principal VARCHAR
(
    255
) COMMENT '厂区负责人',
    principal_contact VARCHAR
(
    255
) COMMENT '负责人联系方式',
    after_sale VARCHAR
(
    255
) COMMENT '售后负责人',
    after_sale_contact VARCHAR
(
    255
) COMMENT '售后负责人联系方式',
    project_address VARCHAR
(
    255
) COMMENT '项目地址'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运维信息表';

ALTER TABLE t_strategy
    ADD `strategy_date` varchar(50) DEFAULT NULL COMMENT '策略的date日期';

ALTER TABLE t_strategy
    ADD `strategy_control_id` bigint(20)  DEFAULT NULL COMMENT '策略的date日期';

CREATE TABLE IF NOT EXISTS t_strategy_template
(
    `id`
    BIGINT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    `template_name`
    VARCHAR
(
    128
) not null COMMENT '模版名称',
    `strategy_type` VARCHAR
(
    128
) not null COMMENT '模版充放电模式',
    `project_id` VARCHAR
(
    128
) not null COMMENT '项目id',
    `create_time` bigint
(
    20
) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar
(
    128
) DEFAULT NULL COMMENT '创建者',
    `update_by` varchar
(
    20
) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint
(
    20
) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模版表';



CREATE TABLE IF NOT EXISTS t_strategy_template_item
(
    `id`
    INT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    `template_id`
    BIGINT
    not
    null
    COMMENT
    '模版id',
    `project_id`
    VARCHAR
(
    128
) not null COMMENT '项目id',
    `start_time` time not null COMMENT '开始时间',
    `end_time` time not null COMMENT '结束时间',
    `power` double
(
    22,
    0
) DEFAULT NULL COMMENT '项目id',
    `soc` double
(
    22,
    0
) DEFAULT NULL COMMENT '项目id',
    `type` int
(
    11
) DEFAULT NULL COMMENT '策略item的type',
    `item_control_id` bigint
(
    20
) DEFAULT NULL COMMENT 'control id',
    `create_time` bigint
(
    20
) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar
(
    128
) DEFAULT NULL COMMENT '创建者',
    `update_by` varchar
(
    20
) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint
(
    20
) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模版item表';


ALTER TABLE t_ammeter
    ADD `is_backup` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'isBackup';


CREATE TABLE IF NOT EXISTS t_strategy_month_template_bind
(
    `id`
    BIGINT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    `month`
    TINYINT
    UNSIGNED
    NOT
    NULL
    COMMENT
    'month月份'
    `template_id`
    bigint
    not
    null
    COMMENT
    '模版id',
    `project_id`
    VARCHAR
(
    128
) not null COMMENT '项目id',
    `group_id` VARCHAR
(
    128
) not null COMMENT '分组id',
    `create_time` bigint
(
    20
) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar
(
    128
) DEFAULT NULL COMMENT '创建者',
    `update_by` varchar
(
    20
) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint
(
    20
) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模版表';


ALTER TABLE t_ammeter
    ADD `is_backup` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'isBackup';


CREATE TABLE IF NOT EXISTS t_strategy_item_control
(
    `id`
    BIGINT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    `strategy_item_id`
    BIGINT
    not
    null
    COMMENT
    '模版id',

    `price_difference_controller`
    tinyint
(
    1
) NOT NULL DEFAULT '0' COMMENT '电价差额/kWh开关(false关闭)(true打开）',
    `price_difference` double DEFAULT '0' COMMENT '电价差额',

    `price_benchmark_controller` tinyint
(
    1
) NOT NULL DEFAULT '0' COMMENT '电价定值开关(false关闭)(true打开）',
    `price_benchmark_condition` varchar
(
    128
) DEFAULT NULL COMMENT '电价定值条件',
    `price_benchmark` double DEFAULT '0' COMMENT '电价定值',

    `price_base_value_controller` tinyint
(
    1
) NOT NULL DEFAULT '0' COMMENT '电价最值开关(false关闭)(true打开）',
    `price_base_value_range_condition` varchar
(
    128
) DEFAULT NULL COMMENT '电价最值条件',
    `price_base_value_section_count` int
(
    11
) DEFAULT 0 COMMENT '电价最值段数',

    `price_continue_duration_controller` tinyint
(
    1
) NOT NULL DEFAULT '0' COMMENT '电价连续时长开关(false关闭)(true打开）',
    `price_continue_duration_condition` varchar
(
    128
) DEFAULT NULL COMMENT '电价连续时长条件',
    `price_continue_duration_hour` int
(
    11
) DEFAULT 0 COMMENT '电价连续小时数',

    `create_time` bigint
(
    20
) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar
(
    128
) DEFAULT NULL COMMENT '创建者',
    `update_by` varchar
(
    20
) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint
(
    20
) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='策略item control控制表';



CREATE TABLE IF NOT EXISTS t_strategy_day_template_bind
(
    `id`
    BIGINT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    `month`
    TINYINT
    UNSIGNED
    NOT
    NULL
    COMMENT
    'month月份',
    `day`
    TINYINT
    UNSIGNED
    NOT
    NULL
    COMMENT
    'day天',
    `template_id`
    bigint
    not
    null
    COMMENT
    '模版id',
    `project_id`
    VARCHAR
(
    128
) not null COMMENT '项目id',
    `group_id` VARCHAR
(
    128
) not null COMMENT '分组id',
    `create_time` bigint
(
    20
) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar
(
    128
) DEFAULT NULL COMMENT '创建者',
    `update_by` varchar
(
    20
) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint
(
    20
) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模版表';

-- 添加电表协议字段
alter table t_controllable
    add dev varchar(20) default null comment '设备名称';
alter table t_controllable
    add baud_rate int default null comment '波特率';
alter table t_controllable
    add byte_size int default null comment '数据位';
alter table t_controllable
    add stop_bits int default null comment '停止位';
alter table t_controllable
    add parity varchar(1) default null comment '奇偶校验';
alter table t_controllable
    add max_temp float default null comment '最高温度';
alter table t_controllable
    add min_temp float default null comment '最低温度';
alter table t_controllable
    add control_mode varchar(32) default null comment '控制模式';
alter table t_controllable
    add channel_ids varchar(512) default null comment '通道ID';
alter table t_controllable
    add device_ids varchar(512) default null comment '设备ID';


ALTER TABLE t_device
    ADD `project_run_init_in_data` double COMMENT '投运初始时间InData';
ALTER TABLE t_device
    ADD `project_run_init_out_data` double COMMENT '投运初始时间OutData';

ALTER TABLE t_ammeter
    ADD `project_run_init_in_data` double COMMENT '投运初始时间InData';
ALTER TABLE t_ammeter
    ADD `project_run_init_out_data` double COMMENT '投运初始时间OutData';

-- 设备增加水冷预启动时间(Min)
alter table t_device
    add cooler_pre_open_time int default null comment '水冷预启动时间(Min)';

-- 新增场站权限
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60020, 1, '/homePage/loadRegulation', '负荷调控');
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60021, 1, '/homePage/iframe', 'iframe');

INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60022, 3, '/group/configuration', '项目配置');
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60023, 60022, '/configuration/add', '新增配置');
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60024, 60022, '/configuration/update', '更新配置');
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60025, 60022, '/configuration/order', '配置排序');
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60026, 60022, '/configuration/delete', '删除配置');
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60027, 60022, '/configuration/list', '查看配置列表');

INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60028, 12, '/system/maintenance', '运维信息');
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60029, 60028, '/maintenance/query', '查看运维信息');
INSERT INTO t_authority (id, pid, ap_key, name)
VALUES (60030, 60028, '/maintenance/update', '更新运维信息');

DELETE
from t_authority
where name = '时间策略导入';
UPDATE t_authority
set ap_key = '/homePage/hookup',
    name   = '一次接线图'
where name = '接线图密码';

-- 分组 首页系统配图增加选项
alter table t_group modify `diagram_of_system` enum('0','1','2','3') NOT NULL DEFAULT '0' COMMENT '首页系统配图(0:储能柜 1:集装箱 2:天吴C 3:PowerCore)';

ALTER TABLE `t_strategy`
    ADD INDEX `idx_groupid_weekday_starttime` (`group_id`, `week_day`, `start_time`);
ALTER TABLE `t_strategy`
    ADD INDEX `idx_projectid_weekday` (`project_id`, `week_day`);
ALTER TABLE `t_strategy_control`
    ADD INDEX `idx_strategydate_projectid_groupid` (`strategy_date`, `project_id`, `group_id`);

alter table t_group
    add day_report_data_interval int default 60 comment '日报间隔时间(Min)';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUE(10112,	10,	'/diagram/getTemperatureRate',	'温控曲线'),

CREATE TABLE `t_capacity_email`
(
    `id`          varchar(128) NOT NULL COMMENT '通知识符uuid',
    `email`       varchar(32)  DEFAULT NULL COMMENT '邮箱',
    `project_id`  varchar(128) DEFAULT NULL COMMENT '项目id',
    `create_by`   varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

<<<<<<< HEAD
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (10223, 28, '/capacity/email/add', '增加通知'),
       (10224, 28, '/capacity/email/edit', '修改通知'),
       (10225, 28, '/capacity/email/delete', '删除通知'),
       (10226, 28, '/capacity/email/query', '查询通知');


alter table `t_ammeter`
    add `connect_type` varchar(50) DEFAULT 'SERIAL_PORT' COMMENT '连接方式(串口serial_port或者web_socket)';

alter table `t_ammeter`
    add `ws_device_type` varchar(50) DEFAULT null COMMENT 'ws_device_type';

alter table `t_ammeter`
    add `ws_device_name` varchar(50) DEFAULT null COMMENT 'ws_device_name';

ALTER TABLE t_strategy_control
    CHANGE COLUMN price_continue_duration_hour price_continue_duration_segment INT (11) DEFAULT 0 COMMENT '电价连续段数';
ALTER TABLE t_strategy_template_item_control
    CHANGE COLUMN price_continue_duration_hour price_continue_duration_segment INT (11) DEFAULT 0 COMMENT '电价连续段数';

CREATE TABLE `t_back_flow_limit`
(
    `id`                         int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id`                 varchar(128) DEFAULT NULL COMMENT '项目id',
    `group_id`                   varchar(128) DEFAULT NULL COMMENT '分组id',
    `system_strategy_id`         int NOT NULL COMMENT '分组对应的主策略的id',
    `start_time`                 time         DEFAULT NULL,
    `end_time`                   time         DEFAULT NULL,
    `back_flow_limit_controller` boolean      DEFAULT false COMMENT 'item防逆流开关',
    `back_flow_limit_power` double DEFAULT 0.0 COMMENT '防逆流power',
    `create_by`                  varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time`                bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`                  varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time`                bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
);

ALTER TABLE t_group
    ADD `time_sharing_back_flow_limit_controller` boolean default false COMMENT '分时防逆流开关';

ALTER TABLE t_group
    ADD `time_sharing_demand_controller` boolean default false COMMENT '分时控需开关';


CREATE TABLE `t_time_sharing_demand`
(
    `id`                   int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id`           varchar(128) DEFAULT NULL COMMENT '项目id',
    `group_id`             varchar(128) DEFAULT NULL COMMENT '分组id',
    `system_strategy_id`   int NOT NULL COMMENT '分组对应的主策略的id',
    `start_time`           time         DEFAULT NULL,
    `end_time`             time         DEFAULT NULL,
    `demand_item_controller` boolean      DEFAULT false COMMENT 'item需量开关',
    `demand_month_control_power` double DEFAULT 0.0 COMMENT '月初需量power',
    `demand_control_power` double DEFAULT 0.0 COMMENT '需量power',
    `create_by`            varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time`          bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`            varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time`          bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
);

CREATE TABLE `t_alarm_config` (
      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `project_id` varchar(128) NOT NULL COMMENT '项目ID',
      `alarm_content` tinyint(1) DEFAULT NULL COMMENT '告警内容',
      `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0:禁用 1:启用)',
      `profit_deviation_coefficient` float DEFAULT NULL COMMENT '收益偏差系数',
      `downtime_threshold` int(11) DEFAULT NULL COMMENT '停机时长阈值(分钟)',
      `offline_count_threshold` int(11) DEFAULT NULL COMMENT '离线状态次数',
      `efficiency_reminder_threshold` float DEFAULT NULL COMMENT '效率提醒阈值',
      `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
      `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
      `update_by` varchar(128) DEFAULT NULL COMMENT '修改者',
      `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
      PRIMARY KEY (`id`),
      KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警配置表';

-- 告警通知配置表
CREATE TABLE `t_alarm_notification` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `alarm_id` int(11) NOT NULL COMMENT '告警ID',
    `user_name` varchar(100) DEFAULT NULL COMMENT '用户名称',
    `notification_type` tinyint(1) DEFAULT NULL COMMENT '告警通知方式(email:邮箱,sms:短信,wechat:微信)',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `create_by` varchar(128) DEFAULT NULL COMMENT '添加人',
    `create_time` bigint(20) DEFAULT NULL COMMENT '添加时间',
    `update_by` varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_alarm_id` (`alarm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警通知配置表';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
                                                              (10223,	28,	'/capacity/email/add',	'增加通知'),
                                                              (10224,	28,	'/capacity/email/edit',	'修改通知'),
                                                              (10225,	28,	'/capacity/email/delete',	'删除通知'),
                                                              (10226,	28,	'/capacity/email/query',	'查询通知');


ALTER TABLE t_device
    ADD `device_control_model` varchar(100) default 'PANGU_CONTROL' COMMENT 'device控制模式';
