<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.electric.mapper.ElectricDynamicPeriodMapper">
    <resultMap id="BaseResultMap" type="com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="device_id" property="deviceId"/>
        <result column="period_start_time" property="periodStartTime"/>
        <result column="period_end_time" property="periodEndTime"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="charge_quantity" property="chargeQuantity"/>
        <result column="charge_cost" property="chargeCost"/>
        <result column="discharge_quantity" property="dischargeQuantity"/>
        <result column="discharge_benefit" property="dischargeBenefit"/>
        <result column="buy_price" property="buyPrice"/>
        <result column="sell_price" property="sellPrice"/>
        <result column="total_benefit" property="totalBenefit"/>
    </resultMap>

    <select id="queryElectricTotalProfit" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select sum(charge_quantity) as charge_quantity,
        sum(charge_cost) as charge_cost,
        sum(discharge_quantity) as discharge_quantity,
        sum(discharge_benefit) as discharge_benefit,
        sum(total_benefit) as total_benefit
        from t_electric_dynamic_period where <![CDATA[ period_start_time >= #{start} and period_start_time < #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
    </select>

    <select id="queryElectricTimeSharingProfit" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select sum(charge_quantity) as charge_quantity,
        sum(charge_cost) as charge_cost,
        sum(discharge_quantity) as discharge_quantity,
        sum(discharge_benefit) as discharge_benefit,
        sum(total_benefit) as total_benefit,
        period_start_time, period_end_time,
        sell_price,
        buy_price
        from t_electric_dynamic_period
        where <![CDATA[ period_start_time >= #{start} and period_start_time < #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        group by period_start_time , period_end_time , sell_price, buy_price
        ORDER BY period_start_time asc
    </select>

    <select id="queryElectricEveryDayProfit" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select sum(charge_quantity) as charge_quantity,
        sum(charge_cost) as charge_cost,
        sum(discharge_quantity) as discharge_quantity,
        sum(discharge_benefit) as discharge_benefit,
        sum(total_benefit) as total_benefit,
        year, month
        <if test="!timeRangeWithin31Day">
            , day
        </if>
        from t_electric_dynamic_period
        where <![CDATA[ period_start_time >= #{start} and period_start_time < #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        group by year, month
        <if test="!timeRangeWithin31Day">
            , day
        </if>
        ORDER BY year, month
        <if test="!timeRangeWithin31Day">
            , day
        </if>
    </select>

    <select id="queryElectricSumGroupByDeviceId" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select device_id, sum(charge_quantity) as charge_quantity,
        sum(discharge_quantity) as discharge_quantity from t_electric_dynamic_period
        where <![CDATA[ period_start_time >= #{start} and period_start_time  < #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId} group by device_id
    </select>

    <select id="queryElectricMonthGroupByDeviceId"
            parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select device_id, year, month, day, sum(charge_quantity) as charge_quantity , sum(discharge_quantity) as
        discharge_quantity
        from t_electric_dynamic_period where <![CDATA[ period_start_time >= #{start} and period_start_time <= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        GROUP BY device_id, year, month, day
        order by year, month, day
    </select>

    <select id="queryElectricYear" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select device_id, year, month, sum(charge_quantity) as charge_quantity,
        sum(discharge_quantity) as discharge_quantity
        from t_electric_dynamic_period where <![CDATA[ period_start_time >= #{start} and period_start_time<= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        group by device_id, year, month order by year, month
    </select>

</mapper>
