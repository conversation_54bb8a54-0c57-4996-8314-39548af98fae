package com.wifochina.modules.strategy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity;
import com.wifochina.modules.strategy.mapper.TimeSharingBackFlowLimitMapper;
import com.wifochina.modules.strategy.request.TimeSharingBackFlowLimitRequest;
import com.wifochina.modules.strategy.service.TimeSharingBackFlowLimitService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;

/**
 * Created on 2025/7/23 11:22.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackOn = Exception.class)
@AllArgsConstructor
public class TimeSharingBackFlowLimitServiceImpl
        extends ServiceImpl<TimeSharingBackFlowLimitMapper, TimeSharingBackFlowLimitEntity>
        implements TimeSharingBackFlowLimitService {

    private final ProjectService projectService;

    @Override
    public List<TimeSharingBackFlowLimitEntity> listBy(String projectId, String groupId) {
        return getBaseMapper()
                .selectList(
                        new LambdaQueryWrapper<TimeSharingBackFlowLimitEntity>()
                                .eq(TimeSharingBackFlowLimitEntity::getProjectId, projectId)
                                .eq(TimeSharingBackFlowLimitEntity::getGroupId, groupId));
    }

    @Override
    public void addBackFlowLimitItem(
            TimeSharingBackFlowLimitRequest timeSharingBackFlowLimitRequest) {
        ProjectEntity projectEntity =
                projectService.getById(timeSharingBackFlowLimitRequest.getProjectId());
        checkTime(projectEntity, timeSharingBackFlowLimitRequest);
        // add
        TimeSharingBackFlowLimitEntity timeSharingBackFlowLimitEntity =
                new TimeSharingBackFlowLimitEntity();
        BeanUtils.copyProperties(timeSharingBackFlowLimitRequest, timeSharingBackFlowLimitEntity);
        timeSharingBackFlowLimitEntity.setId(null);
        getBaseMapper().insert(timeSharingBackFlowLimitEntity);
    }

    @Override
    public void editBackFlowLimitItem(
            TimeSharingBackFlowLimitRequest timeSharingBackFlowLimitRequest) {
        ProjectEntity projectEntity =
                projectService.getById(timeSharingBackFlowLimitRequest.getProjectId());
        checkTime(projectEntity, timeSharingBackFlowLimitRequest);
        // edit
        TimeSharingBackFlowLimitEntity timeSharingBackFlowLimitEntity =
                new TimeSharingBackFlowLimitEntity();
        //        StrategyEntity strategy = new StrategyEntity();
        BeanUtils.copyProperties(timeSharingBackFlowLimitRequest, timeSharingBackFlowLimitEntity);
        getBaseMapper().updateById(timeSharingBackFlowLimitEntity);
    }

    /** check time over */
    public void checkTime(
            ProjectEntity projectEntity,
            TimeSharingBackFlowLimitRequest timeSharingBackFlowLimitRequest) {
        if (!timeSharingBackFlowLimitRequest.getEndTime().equals(LocalTime.MIDNIGHT)
                && timeSharingBackFlowLimitRequest
                        .getStartTime()
                        .isAfter(timeSharingBackFlowLimitRequest.getEndTime())) {
            throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
        }
        if (!timeSharingBackFlowLimitRequest.getStartTime().equals(LocalTime.MIDNIGHT)
                || !timeSharingBackFlowLimitRequest.getEndTime().equals(LocalTime.MIDNIGHT)) {
            if (timeSharingBackFlowLimitRequest.getStartTime().getHour()
                            == timeSharingBackFlowLimitRequest.getEndTime().getHour()
                    && timeSharingBackFlowLimitRequest.getStartTime().getMinute()
                            == timeSharingBackFlowLimitRequest.getEndTime().getMinute()) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }

        List<TimeSharingBackFlowLimitEntity> list =
                getBaseMapper()
                        .selectList(
                                Wrappers.lambdaQuery(TimeSharingBackFlowLimitEntity.class)
                                        .eq(
                                                TimeSharingBackFlowLimitEntity::getProjectId,
                                                projectEntity.getId())
                                        .eq(
                                                TimeSharingBackFlowLimitEntity::getGroupId,
                                                timeSharingBackFlowLimitRequest.getGroupId())
                                        .ne(
                                                timeSharingBackFlowLimitRequest.getId() != null,
                                                TimeSharingBackFlowLimitEntity::getId,
                                                timeSharingBackFlowLimitRequest.getId())
                                        .orderByAsc(TimeSharingBackFlowLimitEntity::getStartTime));

        // 不需要判断size=0，如果等于0，直接插入数据
        if (list == null || list.isEmpty()) {
            return;
        }
        if (timeSharingBackFlowLimitRequest.getEndTime().toSecondOfDay() == 0) {
            if (list.get(list.size() - 1).getEndTime().toSecondOfDay() == 0) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
            if (timeSharingBackFlowLimitRequest
                    .getStartTime()
                    .isBefore(list.get(list.size() - 1).getEndTime())) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }
        for (TimeSharingBackFlowLimitEntity timeSharingBackFlowLimitEntity : list) {
            // if update ignore itself
            if (Objects.equals(
                    timeSharingBackFlowLimitRequest.getId(),
                    timeSharingBackFlowLimitEntity.getId())) {
                continue;
            }
            if (!timeSharingBackFlowLimitRequest
                    .getEndTime()
                    .isAfter(timeSharingBackFlowLimitEntity.getStartTime())) {
                break;
            }
            if (timeSharingBackFlowLimitRequest
                            .getStartTime()
                            .isBefore(timeSharingBackFlowLimitEntity.getEndTime())
                    || timeSharingBackFlowLimitEntity.getEndTime().toSecondOfDay() == 0) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }
    }
}
