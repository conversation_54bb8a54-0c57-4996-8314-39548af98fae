package com.wifochina.modules.strategy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity;
import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import com.wifochina.modules.strategy.mapper.TimeSharingBackFlowLimitMapper;
import com.wifochina.modules.strategy.mapper.TimeSharingDemandMapper;
import com.wifochina.modules.strategy.request.TimeSharingBackFlowLimitRequest;
import com.wifochina.modules.strategy.request.TimeSharingDemandRequest;
import com.wifochina.modules.strategy.service.TimeSharingBackFlowLimitService;

import com.wifochina.modules.strategy.service.TimeSharingDemandService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.List;
import java.util.Objects;

import javax.transaction.Transactional;

/**
 * Created on 2025/7/23 11:22.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackOn = Exception.class)
@AllArgsConstructor
public class TimeSharingDemandServiceImpl
        extends ServiceImpl<TimeSharingDemandMapper, TimeSharingDemandEntity>
        implements TimeSharingDemandService {

    private final ProjectService projectService;

    @Override
    public List<TimeSharingDemandEntity> listBy(String projectId, String groupId) {
        return getBaseMapper()
                .selectList(
                        new LambdaQueryWrapper<TimeSharingDemandEntity>()
                                .eq(TimeSharingDemandEntity::getProjectId, projectId)
                                .eq(TimeSharingDemandEntity::getGroupId, groupId));
    }

    @Override
    public void addDemandItem(TimeSharingDemandRequest timeSharingDemandRequest) {
        ProjectEntity projectEntity =
                projectService.getById(timeSharingDemandRequest.getProjectId());
        checkTime(projectEntity, timeSharingDemandRequest);
        // add
        TimeSharingDemandEntity timeSharingDemandEntity = new TimeSharingDemandEntity();
        BeanUtils.copyProperties(timeSharingDemandRequest, timeSharingDemandEntity);
        timeSharingDemandEntity.setId(null);
        getBaseMapper().insert(timeSharingDemandEntity);
    }

    @Override
    public void editDemandItem(TimeSharingDemandRequest timeSharingDemandRequest) {
        ProjectEntity projectEntity =
                projectService.getById(timeSharingDemandRequest.getProjectId());
        checkTime(projectEntity, timeSharingDemandRequest);
        // edit
        TimeSharingDemandEntity timeSharingDemandEntity = new TimeSharingDemandEntity();
        BeanUtils.copyProperties(timeSharingDemandRequest, timeSharingDemandEntity);
        getBaseMapper().updateById(timeSharingDemandEntity);
    }

    /** check time over */
    public void checkTime(
            ProjectEntity projectEntity, TimeSharingDemandRequest timeSharingDemandRequest) {
        if (!timeSharingDemandRequest.getEndTime().equals(LocalTime.MIDNIGHT)
                && timeSharingDemandRequest
                        .getStartTime()
                        .isAfter(timeSharingDemandRequest.getEndTime())) {
            throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
        }
        if (!timeSharingDemandRequest.getStartTime().equals(LocalTime.MIDNIGHT)
                || !timeSharingDemandRequest.getEndTime().equals(LocalTime.MIDNIGHT)) {
            if (timeSharingDemandRequest.getStartTime().getHour()
                            == timeSharingDemandRequest.getEndTime().getHour()
                    && timeSharingDemandRequest.getStartTime().getMinute()
                            == timeSharingDemandRequest.getEndTime().getMinute()) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }

        List<TimeSharingDemandEntity> list =
                getBaseMapper()
                        .selectList(
                                Wrappers.lambdaQuery(TimeSharingDemandEntity.class)
                                        .eq(
                                                TimeSharingDemandEntity::getProjectId,
                                                projectEntity.getId())
                                        .eq(
                                                TimeSharingDemandEntity::getGroupId,
                                                timeSharingDemandRequest.getGroupId())
                                        .ne(
                                                timeSharingDemandRequest.getId() != null,
                                                TimeSharingDemandEntity::getId,
                                                timeSharingDemandRequest.getId())
                                        .orderByAsc(TimeSharingDemandEntity::getStartTime));

        // 不需要判断size=0，如果等于0，直接插入数据
        if (list == null || list.isEmpty()) {
            return;
        }
        if (timeSharingDemandRequest.getEndTime().toSecondOfDay() == 0) {
            if (list.get(list.size() - 1).getEndTime().toSecondOfDay() == 0) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
            if (timeSharingDemandRequest
                    .getStartTime()
                    .isBefore(list.get(list.size() - 1).getEndTime())) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }
        for (TimeSharingDemandEntity timeSharingDemandEntity : list) {
            // if update ignore itself
            if (Objects.equals(timeSharingDemandRequest.getId(), timeSharingDemandEntity.getId())) {
                continue;
            }
            if (!timeSharingDemandRequest
                    .getEndTime()
                    .isAfter(timeSharingDemandEntity.getStartTime())) {
                break;
            }
            if (timeSharingDemandRequest
                            .getStartTime()
                            .isBefore(timeSharingDemandEntity.getEndTime())
                    || timeSharingDemandEntity.getEndTime().toSecondOfDay() == 0) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }
    }
}
