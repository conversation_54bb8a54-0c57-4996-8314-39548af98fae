package com.wifochina.modules.alarm.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 告警通知配置请求对象
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@ApiModel(value = "告警通知配置请求")
public class AlarmNotificationRequest {

    @ApiModelProperty(value = "主键ID(更新时必填)")
    private String id;

    @ApiModelProperty(value = "告警ID", required = true)
    private String alarmId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "告警通知方式(email:邮箱,sms:短信,wechat:微信)")
    private Integer notificationType;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;
}
