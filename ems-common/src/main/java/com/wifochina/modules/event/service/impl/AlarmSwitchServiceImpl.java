package com.wifochina.modules.event.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.mapper.AlarmSwitchMapper;
import com.wifochina.modules.event.request.AlarmSwitchRequest;
import com.wifochina.modules.event.request.HideCodePageRequest;
import com.wifochina.modules.event.service.AlarmSwitchService;

import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 告警开关服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class AlarmSwitchServiceImpl extends ServiceImpl<AlarmSwitchMapper, AlarmSwitchEntity>
        implements AlarmSwitchService {

    @Resource
    private ProjectExtService projectExtService;

    @Override
    public IPage<EventCodeEntity> getAlarmSwitchEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest) {
        Page<EventCodeEntity> page = Page.of(
                hideCodePageRequest.getPageNum(), hideCodePageRequest.getPageSize());
        return baseMapper.getAlarmSwitchEventCode(page, projectId, hideCodePageRequest);
    }

    @Override
    public List<AlarmSwitchEntity> getAlarmSwitchList(String projectId) {
        return baseMapper.getAlarmSwitchList(projectId);
    }

    @Override
    public List<String> getAllTypeAlarmSwitchEventCodes(String projectId) {
        return baseMapper.getAllTypeAlarmSwitchEventCodes(projectId);
    }

    @Override
    public List<String> getEmsTypeAlarmSwitchEventCodes(String projectId) {
        return baseMapper.getEmsTypeAlarmSwitchEventCodes(projectId);
    }

    @Override
    public List<String> getMeterTypeAlarmSwitchEventCodes(String projectId) {
        return baseMapper.getMeterTypeAlarmSwitchEventCodes(projectId);
    }

    @Override
    public void updateSwitch(AlarmSwitchRequest alarmSwitchRequest) {
        if (alarmSwitchRequest.getEnableAll()) {
            // 设置全局告警开关
            if (alarmSwitchRequest.getLabel() == 1) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllEmsKernelAlarmEnabled, alarmSwitchRequest.getEnabled())
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (alarmSwitchRequest.getLabel() == 2) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllEmsSubDeviceAlarmEnabled, alarmSwitchRequest.getEnabled())
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (alarmSwitchRequest.getLabel() == 3) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllMeterAlarmEnabled, alarmSwitchRequest.getEnabled())
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
        } else {
            // 设置单个告警开关
            if (alarmSwitchRequest.getLabel() == 1) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllEmsKernelAlarmEnabled, false)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (alarmSwitchRequest.getLabel() == 2) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllEmsSubDeviceAlarmEnabled, false)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (alarmSwitchRequest.getLabel() == 3) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getAllMeterAlarmEnabled, false)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }

            List<AlarmSwitchEntity> list =
                    alarmSwitchRequest.getEventCode() == null
                            ? new ArrayList<>()
                            : alarmSwitchRequest.getEventCode().stream()
                            .map(
                                    e ->
                                            new AlarmSwitchEntity(
                                                    WebUtils.projectId.get(),
                                                    e,
                                                    alarmSwitchRequest.getLabel().toString()))
                            .collect(Collectors.toList());

            // 设置启用状态
            list.forEach(entity -> entity.setEnabled(alarmSwitchRequest.getEnabled()));

            // 先删除现有配置，再保存新配置
            remove(
                    Wrappers.lambdaQuery(AlarmSwitchEntity.class)
                            .eq(AlarmSwitchEntity::getProjectId, WebUtils.projectId.get())
                            .eq(AlarmSwitchEntity::getType, alarmSwitchRequest.getLabel().toString()));
            saveBatch(list);
        }
    }

    @Override
    public Map<String, Object> listAlarmSwitch(HideCodePageRequest alarmSwitchPageRequest) {
        IPage<EventCodeEntity> page =
                getAlarmSwitchEventCode(
                        WebUtils.projectId.get(), alarmSwitchPageRequest);
        ProjectExtEntity projectExtEntity = projectExtService.getById(WebUtils.projectId.get());
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> result = new HashMap<>();
        try {
            if (page != null) {
                // 将 page 转为 Map
                result = objectMapper.convertValue(page, new TypeReference<>() {});
            }
            // 增加自定义属性
            result.put("allEmsKernelAlarmEnabled", projectExtEntity.getAllEmsKernelAlarmEnabled());
            result.put("allEmsSubDeviceAlarmEnabled", projectExtEntity.getAllEmsSubDeviceAlarmEnabled());
            result.put("allMeterAlarmEnabled", projectExtEntity.getAllMeterAlarmEnabled());
            // 返回带有额外属性的结果
            return result;
        } catch (Exception e) {
           log.error("转换失败: " + e.getMessage(), e);
        }
        return result;
    }

    @Override
    public void batchUpdateSwitch(AlarmSwitchRequest alarmSwitchRequest) {
        List<AlarmSwitchEntity> list =
                alarmSwitchRequest.getEventCode() == null
                        ? new ArrayList<>()
                        : alarmSwitchRequest.getEventCode().stream()
                        .map(
                                e ->
                                        new AlarmSwitchEntity(
                                                WebUtils.projectId.get(),
                                                e,
                                                alarmSwitchRequest.getLabel().toString()))
                        .collect(Collectors.toList());

        // 设置启用状态
        list.forEach(entity -> entity.setEnabled(alarmSwitchRequest.getEnabled()));

        // 先删除现有配置，再保存新配置
        remove(
                Wrappers.lambdaQuery(AlarmSwitchEntity.class)
                        .eq(AlarmSwitchEntity::getProjectId, WebUtils.projectId.get())
                        .eq(AlarmSwitchEntity::getType, alarmSwitchRequest.getLabel().toString())
                        .in(AlarmSwitchEntity::getEventCode, alarmSwitchRequest.getEventCode()));
        saveBatch(list);
    }

    @Override
    public void allUpdateSwitch(AlarmSwitchRequest alarmSwitchRequest) {
        // 设置所有类型的全局告警开关
        projectExtService
                .lambdaUpdate()
                .set(ProjectExtEntity::getAllEmsKernelAlarmEnabled, alarmSwitchRequest.getEnabled())
                .set(ProjectExtEntity::getAllEmsSubDeviceAlarmEnabled, alarmSwitchRequest.getEnabled())
                .set(ProjectExtEntity::getAllMeterAlarmEnabled, alarmSwitchRequest.getEnabled())
                .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                .update();
    }
}
