package com.wifochina.modules.alarm.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.alarm.entity.AlarmNotificationEntity;
import com.wifochina.modules.alarm.request.AlarmNotificationRequest;
import com.wifochina.modules.alarm.service.AlarmNotificationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 告警通知配置 前端控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/alarm/notification")
@Api(value = "alarm-notification", tags = "告警通知配置管理")
public class AlarmNotificationController {

    @Resource
    private AlarmNotificationService alarmNotificationService;

    /**
     * 根据告警ID查询通知配置列表
     */
    @GetMapping("/list")
    @ApiOperation("根据告警ID查询通知配置列表")
    public Result<List<AlarmNotificationEntity>> list(
            @ApiParam(value = "告警ID", required = true) @RequestParam("alarmId") String alarmId) {
        if (alarmId == null || alarmId.isEmpty()) {
            return Result.failure("400", "告警ID不能为空", null);
        }
        List<AlarmNotificationEntity> list = alarmNotificationService.getByAlarmId(alarmId);
        return Result.success(list);
    }

    /**
     * 新增告警通知配置
     */
    @PostMapping("/add")
    @ApiOperation("新增告警通知配置")
    public Result<Object> add(@RequestBody AlarmNotificationRequest request) {
        if (request.getAlarmId() == null || request.getAlarmId().isEmpty()) {
            return Result.failure("400", "告警ID不能为空", null);
        }
        
        AlarmNotificationEntity alarmNotification = new AlarmNotificationEntity();
        BeanUtils.copyProperties(request, alarmNotification);
        
        boolean success = alarmNotificationService.addAlarmNotification(alarmNotification);
        if (success) {
            return Result.success();
        } else {
            return Result.failure("500", "新增失败", null);
        }
    }

    /**
     * 更新告警通知配置
     */
    @PostMapping("/update")
    @ApiOperation("更新告警通知配置")
    public Result<Object> update(@RequestBody AlarmNotificationRequest request) {
        if (request.getId() == null || request.getId().isEmpty()) {
            return Result.failure("400", "ID不能为空", null);
        }
        
        AlarmNotificationEntity alarmNotification = new AlarmNotificationEntity();
        BeanUtils.copyProperties(request, alarmNotification);
        
        boolean success = alarmNotificationService.updateAlarmNotification(alarmNotification);
        if (success) {
            return Result.success();
        } else {
            return Result.failure("500", "更新失败", null);
        }
    }

    /**
     * 删除告警通知配置
     */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除告警通知配置")
    public Result<Object> delete(@PathVariable("id") String id) {
        if (id == null || id.isEmpty()) {
            return Result.failure("400", "ID不能为空", null);
        }
        
        boolean success = alarmNotificationService.deleteAlarmNotification(id);
        if (success) {
            return Result.success();
        } else {
            return Result.failure("500", "删除失败", null);
        }
    }
}
