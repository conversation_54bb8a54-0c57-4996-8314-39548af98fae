package com.wifochina.modules.strategy.service.impl;

import cn.hutool.core.collection.CollUtil;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.DemandControlEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.request.go.GoTimeSlot;
import com.wifochina.modules.group.request.go.GroupGoRequest;
import com.wifochina.modules.group.request.go.YearlyStrategy;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import com.wifochina.modules.strategy.mapper.StrategyMapper;
import com.wifochina.modules.strategy.request.ImportGroupRequest;
import com.wifochina.modules.strategy.request.ImportStrategyRequest;
import com.wifochina.modules.strategy.service.*;

import com.wifochina.modules.strategytemplate.common.TimeSharingBackFlowLimitTransform;
import com.wifochina.modules.strategytemplate.common.TimeSharingDemandTransform;
import com.wifochina.modules.strategytemplate.service.PredicateContext;
import com.wifochina.modules.strategytemplate.service.predicate.DefaultCustomModelStrategy;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Service
@Slf4j
public class StrategyServiceImpl extends ServiceImpl<StrategyMapper, StrategyEntity>
        implements StrategyService {

    @Resource private GroupService groupService;

    @Resource private ProjectService projectService;

    @Resource private RestTemplate restTemplate;

    @Resource private ControllerService controllerService;
    @Resource private DemandService demandService;
    @Resource private TimeSharingBackFlowLimitService timeSharingBackFlowLimitService;
    @Resource private TimeSharingBackFlowLimitTransform timeSharingBackFlowLimitTransform;
    @Resource private TimeSharingDemandService timeSharingDemandService;

    @Resource private TimeSharingDemandTransform timeSharingDemandTransform;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    @Resource private StrategyHistoryService strategyHistoryService;

    @Resource private DefaultCustomModelStrategy defaultCustomModelStrategy;

    @Override
    public Map<Integer, List<StrategyEntity>> getStrategyByGroupId(String groupId) {
        Map<Integer, List<StrategyEntity>> map = new HashMap<>(7);
        List<StrategyEntity> list =
                this.getBaseMapper()
                        .selectList(
                                Wrappers.lambdaQuery(StrategyEntity.class)
                                        .eq(StrategyEntity::getGroupId, groupId)
                                        .orderByAsc(StrategyEntity::getStartTime));
        // 一周七天+系统分组的0
        for (int i = 0; i <= EmsConstants.DAY_NUM_OF_WEEK; i++) {
            int finalI = i;
            List<StrategyEntity> strategyEntities =
                    list.stream()
                            .filter(strategy -> strategy.getWeekDay() == finalI)
                            .collect(Collectors.toList());
            map.put(i, strategyEntities);
        }
        // 每一个分组 我需要去查询一下 当月的 最大需量的值 现在让前端直接通过  getMaxDemandRate 接口去查询了 这里不做查询返回
        return map;
    }

    /**
     * 查询 分组对应的 系统分组的策略
     *
     * @param groupId
     * @return
     */
    @Override
    public StrategyEntity getOuterStrategyByGroupId(String groupId) {
        return this.getBaseMapper()
                .selectOne(
                        Wrappers.lambdaQuery(StrategyEntity.class)
                                .eq(StrategyEntity::getGroupId, groupId)
                                .eq(StrategyEntity::getWeekDay, 0));
        //                                .orderByAsc(StrategyEntity::getStartTime));
    }

    @Override
    public List<StrategyEntity> getOuterStrategyByProjectId(String projectId) {
        return this.getBaseMapper()
                .selectList(
                        Wrappers.lambdaQuery(StrategyEntity.class)
                                .eq(StrategyEntity::getProjectId, projectId)
                                .eq(StrategyEntity::getWeekDay, 0));
        //                                .orderByAsc(StrategyEntity::getStartTime));
    }

    /**
     * 查询某个星期的 策略列表中 满足 (StartTime < localTime < EndTime ) 的策略 取first
     *
     * @param groupId : groupId
     * @param dayOfWeek : dayOfWeek 星期几
     * @param localTime : 时间段 如 02:00
     * @return : StrategyEntity
     */
    @Override
    public StrategyEntity getTimeRangeStrategyByGroupId(
            String groupId, DayOfWeek dayOfWeek, LocalTime localTime) {
        StrategyEntity strategyEntity = this.getOuterStrategyByGroupId(groupId);
        StrategyEntity timeStrategyEntity = null;
        // 获取到今天星期几 对应的 策略list
        List<StrategyEntity> list =
                this.getBaseMapper()
                        .selectList(
                                Wrappers.lambdaQuery(StrategyEntity.class)
                                        .eq(StrategyEntity::getGroupId, groupId)
                                        .eq(
                                                StrategyEntity::getStrategyType,
                                                strategyEntity.getStrategyType())
                                        .eq(StrategyEntity::getWeekDay, dayOfWeek.getValue())
                                        .orderByAsc(StrategyEntity::getStartTime));

        if (CollUtil.isNotEmpty(list)) {
            timeStrategyEntity =
                    list.stream()
                            .filter(
                                    strategy ->
                                            MyTimeUtil.isWithinTimeRange(
                                                    localTime,
                                                    strategy.getStartTime(),
                                                    strategy.getEndTime()))
                            .findFirst()
                            .orElse(null);
            if (timeStrategyEntity != null) {
                log.debug(
                        "find timeStrategyEntity startTime:{}, endTime:{}",
                        timeStrategyEntity.getStartTime(),
                        timeStrategyEntity.getEndTime());
            } else {
                log.debug("not find timeStrategyEntity now:{},  list:{}", localTime, list);
            }
        }
        return timeStrategyEntity;
    }

    /** 时间策略导入 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importTimeStrategy(ImportStrategyRequest importStrategyRequest) {
        List<StrategyEntity> fromList =
                this.getBaseMapper()
                        .selectList(
                                Wrappers.lambdaQuery(StrategyEntity.class)
                                        .eq(
                                                StrategyEntity::getGroupId,
                                                importStrategyRequest.getGroupId())
                                        .eq(
                                                StrategyEntity::getWeekDay,
                                                importStrategyRequest.getFromWeekDay())
                                        .eq(
                                                StrategyEntity::getStrategyType,
                                                importStrategyRequest.getStrategyType())
                                        .orderByAsc(StrategyEntity::getStartTime));

        for (Integer toWeekDay : importStrategyRequest.getToWeekDays()) {
            this.getBaseMapper()
                    .delete(
                            Wrappers.lambdaQuery(StrategyEntity.class)
                                    .eq(
                                            StrategyEntity::getGroupId,
                                            importStrategyRequest.getGroupId())
                                    .eq(
                                            StrategyEntity::getStrategyType,
                                            importStrategyRequest.getStrategyType())
                                    .eq(StrategyEntity::getWeekDay, toWeekDay));
        }
        for (StrategyEntity fromStrategy : fromList) {
            for (Integer toWeekDay : importStrategyRequest.getToWeekDays()) {
                fromStrategy.setWeekDay(toWeekDay);
                fromStrategy.setId(null);
                this.getBaseMapper().insert(fromStrategy);
            }
        }
    }

    /** 时间策略导出 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importGroupStrategy(ImportGroupRequest importGroupRequest) {
        List<StrategyEntity> fromList =
                this.getBaseMapper()
                        .selectList(
                                Wrappers.lambdaQuery(StrategyEntity.class)
                                        .eq(
                                                StrategyEntity::getGroupId,
                                                importGroupRequest.getFromGroupId())
                                        .eq(
                                                StrategyEntity::getStrategyType,
                                                importGroupRequest.getStrategyType()));
        this.getBaseMapper()
                .delete(
                        Wrappers.lambdaQuery(StrategyEntity.class)
                                .eq(StrategyEntity::getGroupId, importGroupRequest.getToGroupId())
                                .eq(
                                        StrategyEntity::getStrategyType,
                                        importGroupRequest.getStrategyType()));

        for (StrategyEntity toStrategy : fromList) {
            GroupEntity groupEntity = groupService.getById(toStrategy.getGroupId());
            // 如果是从系统分组来的数据，则设置并网点控制为空
            if (toStrategy.getWeekDay() != null
                    && toStrategy.getWeekDay() == 0
                    && Boolean.TRUE.equals(groupEntity.getWhetherSystem())) {
                toStrategy.setControlPower(null);
            }
            // 如果导入到系统分组，则需量控制功率设置为空
            if (Boolean.TRUE.equals(groupEntity.getWhetherSystem())) {
                toStrategy.setControlPower(null);
            }
            toStrategy.setId(null);
            toStrategy.setGroupId(importGroupRequest.getToGroupId());
            this.getBaseMapper().insert(toStrategy);
        }
    }

    @Override
    public boolean uploadStrategy(String projectId) {
        // 偷懒了, 就全查了，省得多个方法实现差不多的功能
        List<GroupEntity> groupList =
                groupService.list(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, projectId));
        ProjectEntity projectEntity = projectService.getById(projectId);
        Map<String, GroupEntity> groupMap =
                groupList.stream().collect(Collectors.toMap(GroupEntity::getId, v -> v));
        List<Map<String, Object>> list = getGroupGoRequest(projectEntity, groupList);
        // 月份任务需要手动插入
        WebUtils.projectId.set(projectId);
        try {
            JSONObject jsonObject = uploadStrategyGoRequest(projectId, projectEntity, list);
            if (jsonObject == null) {
                log.error(
                        "上传策略失败, projectName: {}, message: 未收到响应", projectEntity.getProjectName());
                return false;
            }
            if (!(boolean) jsonObject.get(EmsConstants.SUCCESS)) {
                String errorMessage = jsonObject.getString("error_message");
                log.error(
                        "上传策略失败, projectName: {}, message: {}",
                        projectEntity.getProjectName(),
                        errorMessage != null ? errorMessage : "响应错误");
                return false;
            } else {
                // saveHistoryStrategy(projectId);
                // 1.3.9 为了让策略下发就立即记录一下 到influxdb里
                Long end = Instant.now().getEpochSecond();
                list.forEach(
                        map -> {
                            // 给没个分组 写一个最新的 徐良
                            String groupId = (String) map.get("uuid");
                            // 这里有循环调用 谁想优化可以改了.
                            // 1.4.1 需要 写入到曲线里的值 就是平台的 控制power 而不是 * 了比例的
                            StrategyEntity strategyEntity = this.getOuterStrategyByGroupId(groupId);
                            Double gridControlPower = strategyEntity.getGridControlPower();
                            GroupEntity group = groupMap.get(groupId);
                            demandService.saveControlPowerDemandToInfluxDb(
                                    gridControlPower, group, end);
                        });
                // 这里日志记录成功的情况，实际可能需要调整逻辑
                return true;
            }

        } catch (Exception e) {
            log.error(
                    "上传策略失败, projectName: {}, message: {}",
                    projectEntity.getProjectName(),
                    "网络错误",
                    e);
            return false;
        }
    }

    private @Nullable JSONObject uploadStrategyGoRequest(
            String projectId, ProjectEntity projectEntity, List<Map<String, Object>> list) {
        JSONObject jsonObject;
        if (projectEntity.getProjectModel() == 1) {
            ControllerEntity controllerEntity =
                    controllerService.getOne(
                            Wrappers.lambdaQuery(ControllerEntity.class)
                                    .eq(ControllerEntity::getProjectId, projectId));
            String outerControllerUrl =
                    "http://" + controllerEntity.getIp().trim() + ":" + controllerEntity.getPort();
            jsonObject =
                    restTemplate.postForObject(
                            outerControllerUrl + "/api/v1/update_strategy", list, JSONObject.class);
        } else {
            jsonObject =
                    restTemplate.postForObject(
                            gatewayUrl + "/api/v1/update_strategy", list, JSONObject.class);
        }
        return jsonObject;
    }

    private @NotNull List<Map<String, Object>> getGroupGoRequest(
            ProjectEntity projectEntity, List<GroupEntity> groupList) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (GroupEntity group : groupList) {
            GroupGoRequest groupGoRequest = this.getGroupGoRequest(projectEntity, group.getId());
            Map<String, Object> map = new HashMap<>(2);
            map.put("uuid", group.getId());
            map.put("strategy", groupGoRequest);
            list.add(map);
        }
        return list;
    }

    /**
     * 之所有方法加在这里，是为了让那边能够在service层复用
     *
     * @return GroupGoRequest
     */
    @Override
    public GroupGoRequest getGroupGoRequest(ProjectEntity projectEntity, String groupId) {
        GroupEntity groupEntity = groupService.getById(groupId);
        GroupGoRequest groupGoRequest = new GroupGoRequest();
        groupGoRequest.setStrategyPriority(groupEntity.getPriority());
        StrategyEntity strategyEntity = this.getOuterStrategyByGroupId(groupId);
        // 1.4.2 added 微网储能SOC被动均衡
        groupGoRequest.setMgSocBalance(groupEntity.getMgSocBalance());
        groupGoRequest.setMgSocBalanceOffGridPowerLimitRatio(
                groupEntity.getMgSocBalanceOffGridPowerLimitRatio());
        // 是否开启 vpp
        groupGoRequest.setVpp(groupEntity.getOpenVpp());
        // 放电soc下限
        Optional.ofNullable(strategyEntity.getSoc())
                .ifPresent(e -> groupGoRequest.setDischargeSocLowLimit(e.intValue()));
        Optional.ofNullable(strategyEntity.getOffGirdSoc())
                .ifPresent(e -> groupGoRequest.setOffGridSocLimit(e.intValue()));
        // 并离网soc上限
        Optional.ofNullable(strategyEntity.getChargeSocHighLimit())
                .ifPresent(e -> groupGoRequest.setChargeSocHighLimit(e.intValue()));
        Optional.ofNullable(strategyEntity.getOffGridSocHighLimit())
                .ifPresent(e -> groupGoRequest.setOffGridSocHighLimit(e.intValue()));
        // 策略优先级
        Optional.ofNullable(strategyEntity.getPriority())
                .ifPresent(groupGoRequest::setStrategyPriority);
        // 防逆流开 默认不开
        if (Boolean.TRUE.equals(groupEntity.getTimeSharingBackFlowLimitController())) {
            List<TimeSharingBackFlowLimitEntity> backFlowLimitEntities =
                    timeSharingBackFlowLimitService.listBy(projectEntity.getId(), groupId);
            if (CollectionUtil.isNotEmpty(backFlowLimitEntities)) {
                // 把 分时防逆流转换成对应结构 下发下去
                List<GoTimeSlot> goTimeSlots =
                        timeSharingBackFlowLimitTransform.transform(
                                backFlowLimitEntities,
                                transformContext -> {
                                    transformContext.project = projectEntity;
                                    transformContext.setGroup(groupEntity);
                                    return null;
                                });
                groupGoRequest.setBackFlowTimeSlot(goTimeSlots);
            }
        } else {
            // old 逻辑
            Optional.ofNullable(strategyEntity.getAntiReflux())
                    .ifPresent(
                            e -> {
                                groupGoRequest.setBackFlowPreventing(e);
                                groupGoRequest.setBackFlowLimitPower(
                                        strategyEntity.getBackFlowLimitPower());
                            });
        }

        Optional.ofNullable(strategyEntity.getChargeInAppointTime())
                .ifPresent(groupGoRequest::setBackFlowPreventingNotAllowCharge);
        Optional.ofNullable(strategyEntity.getDischargeInAppointTime())
                .ifPresent(groupGoRequest::setBackFlowPreventingNotAllowDischarge);
        // 设置功率因子相关
        groupGoRequest.setPowerFactorControl(strategyEntity.getPowerFactorControl());
        groupGoRequest.setPowerFactorFirst(strategyEntity.getPowerFactorFirst());
        groupGoRequest.setPowerFactorControlValue(strategyEntity.getPowerFactorControlValue());
        // 设置是否开启测控略soc
        groupGoRequest.setEnableStopSoc(
                (groupEntity.getEnableStopSoc() != null && groupEntity.getEnableStopSoc()) ? 1 : 0);
        // 如果是系统分组
        if (Boolean.TRUE.equals(groupEntity.getWhetherSystem())) {
            groupGoRequest.setSystem(true);
        }
        if (groupEntity.getCapacityController() != null && groupEntity.getCapacityController()) {
            // 容量控制
            Optional.ofNullable(strategyEntity.getControlPower())
                    .ifPresent(groupGoRequest::setRequiredPower);
            // 设计需量
            Optional.ofNullable(strategyEntity.getPccDemandPower())
                    .ifPresent(groupGoRequest::setDemandPower);
        }
        // 1.4.0 把收益和控制开关合并到一个 字段里了 叫 DemandControl 字段
        // 如果开了分组需量开关
        /**
         * 1、自动抬升逻辑描述： 1.1、每月1号0点，当前需量控制功率(kW)=初需量控制功率(kW) 1.2、如果最大需量大于 当前需量控制功率(kW)，且最大需量*自动调整比例(%)
         * 小于等于 需量调整上限(kW) ，当前需量控制功率(kW) 自动填入 最大需量(kW)*自动调整比例(%)
         * 并自动保存/下发，实际下发到协调控制器的控制需量值=最大需量(kW)*自动调整比例(%) * 需量控制系数(%) 1.3、如果最大需量大于
         * 当前需量控制功率(kW)，且最大需量*自动调整比例(%) 大于 需量调整上限(kW) ，当前需量控制功率(kW) 自动填入 需量调整上限(kW)
         * 并自动保存/下发，实际下发到协调控制器的控制需量值=需量调整上限(kW) * 需量控制系数(%)
         *
         * <p>2、手动抬升逻辑描述： 2.1、每月1号0点，当前需量控制功率(kW)=初需量控制功率(kW) 2.2、如果最大需量大于 当前需量控制功率(kW)，可手动调整
         * 当前需量控制功率(kW) ，实际下发到协调控制器的控制需量值=当前需量控制功率(kW) * 需量控制系数(%)
         */
        // 如果是手动保持以前的
        if (DemandControlEnum.enableDemandControl(groupEntity.getDemandControl())) {
            // 1.4.4 added 分时需量控制
            if (Boolean.TRUE.equals(groupEntity.getTimeSharingDemandController())) {
                List<TimeSharingDemandEntity> timeSharingDemandEntities =
                        timeSharingDemandService.listBy(projectEntity.getId(), groupId);
                if (CollectionUtil.isNotEmpty(timeSharingDemandEntities)) {
                    // 把 分时控需转换成对应结构 下发下去
                    List<GoTimeSlot> goTimeSlots =
                            timeSharingDemandTransform.transform(
                                    timeSharingDemandEntities,
                                    transformContext -> {
                                        transformContext.project = projectEntity;
                                        transformContext.setGroup(groupEntity);
                                        return null;
                                    });
                    groupGoRequest.setGirdControlTimeSlot(goTimeSlots);
                }

            } else {
                // 自动抬升那边已经把值 更改到了 girdControlPower上了
                Optional.ofNullable(strategyEntity.getGridControlPower())
                        .ifPresent(
                                e ->
                                        groupGoRequest.setGridControlPower(
                                                e * (groupEntity.getDemandControlRate() / 100)));
            }
        }
        // 外部控制器模式开关
        Optional.ofNullable(groupEntity.getExternalController())
                .ifPresent(groupGoRequest::setDirectPowerControl);
        Optional.ofNullable(groupEntity.getDirectPowerAutoControl())
                .ifPresent(groupGoRequest::setDirectPowerAutoControl);
        Optional.ofNullable(groupEntity.getDirectPowerControlIec104EnableIoa())
                .ifPresent(groupGoRequest::setDirectPowerControlIec104EnableIoa);
        Optional.ofNullable(groupEntity.getDirectPowerControlIec104CommonAddr())
                .ifPresent(groupGoRequest::setDirectPowerControlIec104CommonAddr);
        Optional.ofNullable(groupEntity.getDirectPowerControlIec104ActiveIoa())
                .ifPresent(groupGoRequest::setDirectPowerControlIec104ActiveIoa);
        Optional.ofNullable(groupEntity.getDirectPowerControlIec104ReactiveIoa())
                .ifPresent(groupGoRequest::setDirectPowerControlIec104ReactiveIoa);
        Optional.ofNullable(groupEntity.getDirectPowerControlModbusActiveAddr())
                .ifPresent(groupGoRequest::setDirectPowerControlModbusActiveAddr);
        Optional.ofNullable(groupEntity.getDirectPowerControlModbusReactiveAddr())
                .ifPresent(groupGoRequest::setDirectPowerControlModbusReactiveAddr);
        // 断言处理strategy 整一个开关 开关打开 走新逻辑 下发yearly strategy?
        // 2025-05-20 13:48:38 annotation this piece of code temp
        // 2025-04-29 15:51:04 added new yearly strategy
        Map<String, List<YearlyStrategy>> stringListMap =
                defaultCustomModelStrategy.executeModelPredicate(
                        new PredicateContext(projectEntity, groupEntity, null, null));
        groupGoRequest.setYearlyStrategies(stringListMap);

        Map<String, Object> controllableStrategies =
                JSON.parseObject(groupEntity.getControllableStrategies(), new TypeReference<>() {});
        groupGoRequest.setControllableStrategies(controllableStrategies);

        Map<String, Object> emsStrategies =
                JSON.parseObject(groupEntity.getEmsStrategies(), new TypeReference<>() {});
        groupGoRequest.setEmsStrategies(emsStrategies);

        return groupGoRequest;
    }

    @Override
    public List<StrategyEntity> getTimeStrategyByGroupIdAndType(String groupId, int type) {
        return this.lambdaQuery()
                .eq(StrategyEntity::getGroupId, groupId)
                .eq(StrategyEntity::getStrategyType, type)
                .ne(StrategyEntity::getWeekDay, 0)
                .orderByAsc(StrategyEntity::getWeekDay)
                .orderByAsc(StrategyEntity::getStartTime)
                .list();
    }
}
