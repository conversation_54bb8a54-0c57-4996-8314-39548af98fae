package com.wifochina.modules.transmission50100;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.page.Result;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

/**
 * Created on 2025/7/30 09:25.
 *
 * <AUTHOR>
 */
@Api(tags = "50-100透传接口")
@RestController
@RequestMapping("/transmission")
@RequiredArgsConstructor
@Slf4j
public class TransmissionController {

    @Autowired private RestTemplate restTemplate;
    @Autowired private ProjectService projectService;
    @Autowired private ControllerService controllerService;
    private static final String TRANSPORTER_API = "/api/v1/pcs_transporter";

    @Value("${ems.gateway}")
    private String gatewayUrl;

    /** ems-单机模式 */
    @PostMapping("/send")
    @ApiOperation("透传50-100数据接口")
    public Result<String> send(@RequestBody String rawJson) {
        // 设置 headers
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        String url = "";
        if (projectEntity.getProjectModel() == 1) {
            ControllerEntity controllerEntity =
                    controllerService.getOne(
                            Wrappers.lambdaQuery(ControllerEntity.class)
                                    .eq(ControllerEntity::getProjectId, projectId));
            String outerControllerUrl =
                    "http://" + controllerEntity.getIp().trim() + ":" + controllerEntity.getPort();
            url = outerControllerUrl + TRANSPORTER_API;
        } else {
            url = gatewayUrl + TRANSPORTER_API;
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> requestEntity = new HttpEntity<>(rawJson, headers);
        // 发送 POST 请求
        ResponseEntity<String> response =
                restTemplate.postForEntity(url, requestEntity, String.class);
        return Result.success(response.getBody());
    }
}
