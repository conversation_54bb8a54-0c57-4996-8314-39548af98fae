package com.wifochina.modules.strategytemplate.service.impl

import com.baomidou.mybatisplus.core.toolkit.Wrappers.lambdaQuery
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategy.request.ImportGroupRequest
import com.wifochina.modules.strategytemplate.converter.StrategyControlConverter
import com.wifochina.modules.strategytemplate.converter.StrategyConverter
import com.wifochina.modules.strategytemplate.converter.StrategyDayTemplateBindConverter
import com.wifochina.modules.strategytemplate.converter.StrategyMonthTemplateBindConverter
import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity
import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyMonthTemplateBindEntity
import com.wifochina.modules.strategytemplate.service.StrategyControlService
import com.wifochina.modules.strategytemplate.service.StrategyDayTemplateBindService
import com.wifochina.modules.strategytemplate.service.StrategyImportService
import com.wifochina.modules.strategytemplate.service.StrategyMonthTemplateBindService
import com.wifochina.modules.strategytemplate.service.StrategyServiceKt
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.lang.Boolean
import kotlin.Exception
import kotlin.apply

/**
 * Created on 2025/5/31 11:38.
 * <AUTHOR>
 */
@Service
class StrategyImportServiceImpl(
    val strategyMonthTemplateBindService: StrategyMonthTemplateBindService,
    val strategyDayTemplateBindService: StrategyDayTemplateBindService,
    val strategyControlService: StrategyControlService,
    val strategyServiceKt: StrategyServiceKt,

    ) : StrategyImportService {

    @Transactional(rollbackFor = [Exception::class])
    override fun importGroupStrategy(importGroupRequest: ImportGroupRequest) {
        val projectId = WebUtils.projectId.get()
        //1.导入 t_strategy_month_template_bind信息
        val fromGroupId = importGroupRequest.fromGroupId
        val toGroupId = importGroupRequest.toGroupId
        val monthBindInfos = strategyMonthTemplateBindService.list(
            KtQueryWrapper(StrategyMonthTemplateBindEntity::class.java).eq(
                StrategyMonthTemplateBindEntity::projectId, projectId
            ).eq(StrategyMonthTemplateBindEntity::groupId, fromGroupId)
        )

        val importMonthBindInfos = monthBindInfos.map {
            StrategyMonthTemplateBindConverter.INSTANCE.copy(it).apply {
                id = null
                groupId = toGroupId
                createBy = null
                createTime = null
                updateBy = null
                updateTime = null
            }
        }
        strategyMonthTemplateBindService.remove(
            KtQueryWrapper(StrategyMonthTemplateBindEntity::class.java).eq(
                StrategyMonthTemplateBindEntity::projectId, projectId
            ).eq(StrategyMonthTemplateBindEntity::groupId, toGroupId)
        )
        strategyMonthTemplateBindService.saveBatch(importMonthBindInfos)

        val importDayBindInfos = strategyDayTemplateBindService.list(
            KtQueryWrapper(StrategyDayTemplateBindEntity::class.java).eq(
                StrategyDayTemplateBindEntity::projectId, projectId
            ).eq(StrategyDayTemplateBindEntity::groupId, fromGroupId)
        ).map {
            StrategyDayTemplateBindConverter.INSTANCE.copy(it).apply {
                id = null
                groupId = toGroupId
                createBy = null
                createTime = null
                updateBy = null
                updateTime = null
            }
        }
        strategyDayTemplateBindService.remove(
            KtQueryWrapper(StrategyDayTemplateBindEntity::class.java).eq(
                StrategyDayTemplateBindEntity::projectId, projectId
            ).eq(StrategyDayTemplateBindEntity::groupId, toGroupId)
        )
        //2.导入 t_strategy_day_template_bind信息
        strategyDayTemplateBindService.saveBatch(importDayBindInfos)

        //3.导入 t_strategy_control 信息
        val importControlInfos = strategyControlService.list(
            KtQueryWrapper(StrategyControlEntity::class.java).eq(StrategyControlEntity::projectId, projectId)
                .eq(StrategyControlEntity::groupId, fromGroupId)
        ).map {
            StrategyControlConverter.INSTANCE.copy(it).apply {
                id = null
                groupId = toGroupId
                createBy = null
                createTime = null
                updateBy = null
                updateTime = null
            }
        }
        strategyControlService.remove(
            KtQueryWrapper(StrategyControlEntity::class.java).eq(
                StrategyControlEntity::projectId, projectId
            ).eq(StrategyControlEntity::groupId, toGroupId)
        )
        strategyControlService.saveBatch(importControlInfos)

        //4.导入 t_strategy 信息
        val importStrategyInfos =
            strategyServiceKt.list(lambdaQuery<StrategyEntity>().eq(StrategyEntity::getProjectId, projectId)
                                       .eq(StrategyEntity::getGroupId, fromGroupId).and { wrapper ->
                    //只考虑 new strategy 策略下的  null 和  0
                    wrapper.isNull(StrategyEntity::getWeekDay).or().eq(StrategyEntity::getWeekDay, 0)
                }).map {
                StrategyConverter.INSTANCE.copy(it).apply {
                    id = null
                    groupId = toGroupId
                    createBy = null
                    createTime = null
                    updateBy = null
                    updateTime = null
                }
            }
        strategyServiceKt.remove(lambdaQuery<StrategyEntity?>().eq(StrategyEntity::getProjectId, projectId)
                                     .eq(StrategyEntity::getGroupId, toGroupId).and { wrapper ->
                //只考虑 new strategy 策略下的  null 和  0
                wrapper.isNull(StrategyEntity::getWeekDay).or().eq(StrategyEntity::getWeekDay, 0)
            })

        importStrategyInfos.filter { it.weekDay != null && it.weekDay == 0 }.forEach {
            // 如果是从系统分组来的数据，则设置并网点控制为空
            it.setControlPower(null)
            // 如果导入到系统分组，则需量控制功率设置为空
            it.setGridControlPower(null)
        }
        strategyServiceKt.saveBatch(importStrategyInfos)
    }
}