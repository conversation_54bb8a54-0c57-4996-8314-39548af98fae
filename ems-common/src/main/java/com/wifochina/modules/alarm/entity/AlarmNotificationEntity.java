package com.wifochina.modules.alarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 告警通知配置实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_alarm_notification")
@ApiModel(value = "AlarmNotificationEntity对象", description = "告警通知配置")
public class AlarmNotificationEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "告警ID")
    private Long alarmId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "告警通知方式(email:邮箱,sms:短信,wechat:微信)")
    private Integer notificationType;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;
}
