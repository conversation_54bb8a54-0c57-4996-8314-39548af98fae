package com.wifochina.modules.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.*;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.demand.entity.GroupDemandMonthIncomeEntity;
import com.wifochina.modules.demand.mapper.GroupDemandMonthIncomeMapper;
import com.wifochina.modules.electric.ElectricTimeSeriesService;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.income.AbstractProfitService;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 4/14/2022 11:25 AM
 */
@Service
@Slf4j
public class NewInfluxOperationProfitServiceImpl extends AbstractProfitService {
    private static final int MAP_DEFAULT_SIZE = 16;
    @Resource private InfluxClientService influxClient;
    @Resource private ElectricTimeSeriesService electricTimeSeriesService;
    @Resource private ThreadPoolTaskExecutor asyncServiceExecutor;
    @Resource private GroupDemandMonthIncomeMapper groupDemandMonthIncomeMapper;

    @Override
    public Double getPvOrWindPower(
            Long startDate, Long endDate, String column, String type, String projectId) {
        return null;
    }

    @Override
    public ElectricProfitVO getTodayBatteryProfit(String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        long endDate = Instant.now().getEpochSecond();
        long startDate = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        ServiceAssert.isTrue(endDate >= startDate, ErrorResultCode.START_GE_END_TIME.value());
        List<DeviceEntity> deviceEntities = deviceService.findIncomeDevices(projectId);
        List<AmmeterEntity> ammeterEntities = ammeterService.findIncomeAmmeter(projectId);
        String queryEmsString = null;
        String queryMeterString = null;
        if (!deviceEntities.isEmpty()) {
            queryEmsString = electricTimeSeriesService.getElectricEmsSql(projectEntity);
            queryEmsString =
                    queryEmsString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createIdSearchSqlForTimeSeries(deviceEntities));
        }
        if (!ammeterEntities.isEmpty()) {
            queryMeterString = electricTimeSeriesService.getElectricMeterSql(projectEntity);
            queryMeterString =
                    queryMeterString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createIdSearchSqlForTimeSeries(ammeterEntities));
        }
        if (queryEmsString == null && queryMeterString == null) {
            // 兼容一下 如果上面的 都是null 表示没有 设备或者电表要去查询 直接返回 并且是 init的后的...
            ElectricProfitVO electricProfitVO = new ElectricProfitVO();
            electricProfitVO.init();
            return electricProfitVO;
        }
        return getTodayElectricPrice(projectId, startDate, queryEmsString, queryMeterString);
    }

    /** 优化了一下 influxdb的版本 */
    @Override
    public ElectricProfitVO getTodayGroupBatteryProfit(String projectId, String groupId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        long endDate = Instant.now().getEpochSecond();
        long startDate = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        ServiceAssert.isTrue(endDate >= startDate, ErrorResultCode.START_GE_END_TIME.value());
        String queryEmsString = null;
        String queryMeterString = null;
        List<String> deviceIds = new ArrayList<>();
        List<String> meterIds = new ArrayList<>();
        if (!StringUtil.isEmpty(groupId)) {
            deviceIds = getIncomeDeviceIdsForGroup(projectId, groupId);
            meterIds = getIncomeAmmeterIdsForGroup(projectId, groupId);
        }
        if (!deviceIds.isEmpty()) {
            queryEmsString = electricTimeSeriesService.getElectricEmsSql(projectEntity);
            queryEmsString =
                    queryEmsString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createDeviceSqlForInfluxDb(deviceIds));
        }
        if (!meterIds.isEmpty()) {
            queryMeterString = electricTimeSeriesService.getElectricMeterSql(projectEntity);
            queryMeterString =
                    queryMeterString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createMeterSqlForInflux(meterIds));
        }
        return getTodayElectricPrice(projectId, startDate, queryEmsString, queryMeterString);
    }

    @NotNull
    @Override
    public String getYesterdayProfitQueryString(long offsetSeconds, String projectId) {
        return null;
    }

    private ElectricProfitVO getTodayElectricPrice(
            String projectId, long startDate, String queryEmsSql, String queryMeterSql) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 获取系统当前的配置价格
        ElectricPriceEntity matchPriceEntity =
                electricPriceService.getElectricPriceMatch(projectId, startDate);
        if (matchPriceEntity == null) {
            ElectricProfitVO electricProfitVO = new ElectricProfitVO();
            electricProfitVO.init();
            log.error(
                    "projectId {} cant find electricPrice not calculate getTodayElectricPrice",
                    projectId);
            return electricProfitVO;
        }
        ElectricProfitVO electricProfitVO = new ElectricProfitVO();
        electricProfitVO.init();
        Lock lock = new ReentrantLock();
        // 获取时间段价格
        List<ElectricPriceEntity> list = matchPriceEntity.getPeriodPriceList();
        TimeContext timeContext = TimeContext.getContext(projectEntity.getTimezone(), startDate);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        for (ElectricPriceEntity price : list) {
            asyncServiceExecutor.submit(
                    () -> {
                        try {
                            todayElectricProfit(
                                    queryEmsSql,
                                    price,
                                    timeContext,
                                    projectEntity,
                                    lock,
                                    electricProfitVO);
                            todayElectricProfit(
                                    queryMeterSql,
                                    price,
                                    timeContext,
                                    projectEntity,
                                    lock,
                                    electricProfitVO);
                        } finally {
                            countDownLatch.countDown();
                        }
                    });
        }
        boolean jobResult;
        try {
            jobResult = countDownLatch.await(3, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error(
                    "getTodayBatteryProfit---> {}",
                    projectEntity.getProjectName()
                            + "- calculate electric earning await produce exception");
            return electricProfitVO;
        }
        if (!jobResult) {
            log.error(
                    "getTodayBatteryProfit---> {}",
                    projectEntity.getProjectName() + "- calculate electric earning await failed");
            return electricProfitVO;
        }
        // 如果是 尖峰平谷的 需要去 合计一下
        if (projectEntity
                .getElectricPriceType()
                .equals(ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD.name())) {
            setElectricProfitVoTotal(electricProfitVO);
        } else if (projectEntity
                        .getElectricPriceType()
                        .equals(ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.name())
                || projectEntity
                        .getElectricPriceType()
                        .equals(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name())) {
            setElectricProfitVoTotalForDynamic(electricProfitVO);
        }
        return electricProfitVO;
    }

    private void setElectricProfitVoTotalForDynamic(ElectricProfitVO electricProfitVO) {
        // 总收益
        electricProfitVO.setTotalBenefit(
                electricProfitVO.getTotalDischargeBenefit()
                        - electricProfitVO.getTotalChargeCost());
    }

    private void todayElectricProfit(
            String queryEmsString,
            ElectricPriceEntity price,
            TimeContext timeContext,
            ProjectEntity projectEntity,
            Lock lock,
            ElectricProfitVO electricProfitVO) {
        // 后期要把这个timeContext 优化一下 目前influxdb 里面是按照 时段的时间去查询的 不需要那么多东西了 ,目前看了下 只用到了
        // oneDayZero
        // 但是lindorm那边还是按照日期 然后去减去或者加上时段的时间 去算的 懒得改了 后面和lindorm 说拜拜了
        electricTimeSeriesService.calculateTimeChargeDiff(
                timeContext,
                price,
                projectEntity,
                queryEmsString,
                (outDiff, inDiff) -> {
                    // 如果是的动态时段 对total相关的属性 多个时段线程 会有安全问题 这里锁一下, 如果是尖峰平谷没问题
                    // 因为是各个不同的字段
                    lock.lock();
                    try {
                        electricAdapterChooser
                                .choose(projectEntity.getElectricPriceType())
                                .calculatePeriodWithElectricProfitVo(
                                        price, electricProfitVO, outDiff, inDiff);
                    } finally {
                        lock.unlock();
                    }
                });
    }

    /** 降低需量功率查询 这个是实时从数据库查询的可能会比较慢 目前1.4.0 查询的地方 使用下面的方法从db里面查询 */
    @Override
    public Map<String, Map<Long, Double>> getDemandIncomePower(
            Long startDate, Long endDate, String projectId) {
        Map<String, Map<Long, Double>> map = new HashMap<>(MAP_DEFAULT_SIZE);
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<String> groupIds =
                groupService.queryEnableDemandIncome(projectId).stream()
                        .map(GroupEntity::getId)
                        .collect(Collectors.toList());
        if (groupIds.isEmpty()) {
            return map;
        }
        Map<String, Map<Long, Double>> meterMap = new HashMap<>(groupIds.size());
        Map<String, Map<Long, Double>> demandMap = new HashMap<>(groupIds.size());
        List<Restrictions> deviceRestrictions = new ArrayList<>(groupIds.size());
        for (String groupId : groupIds) {
            deviceRestrictions.add(Restrictions.tag(EmsConstants.GROUP_ID).equal(groupId));
            meterMap.put(groupId, new HashMap<>(MAP_DEFAULT_SIZE));
            demandMap.put(groupId, new HashMap<>(MAP_DEFAULT_SIZE));
            map.put(groupId, new HashMap<>(MAP_DEFAULT_SIZE));
        }
        Restrictions groupTagFilter =
                Restrictions.or(deviceRestrictions.toArray(new Restrictions[0]));
        Flux flux =
                Flux.from(influxClient.getBucketDemand())
                        .withLocationFixed(
                                MyTimeUtil.getOffsetSecondsFromZoneCode(projectEntity.getTimezone())
                                        + "s")
                        .range(startDate, endDate)
                        .filter(
                                Restrictions.measurement()
                                        .equal(influxClient.getDemandTable(projectId, null)))
                        .filter(Restrictions.tag(EmsConstants.PROJECT_ID).equal(projectId))
                        .filter(groupTagFilter)
                        .aggregateWindow(1L, ChronoUnit.MONTHS, EmsConstants.INFLUX_MAX_FUNC);
        // 将query进行兑换
        String queryString = flux.toString();
        log.debug(queryString);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                Long time = Objects.requireNonNull(record.getTime()).getEpochSecond();
                Double value = (Double) record.getValueByKey("_value");
                String groupId = (String) record.getValueByKey("groupId");
                if ("original_Demand".equals(record.getField())) {
                    demandMap.get(groupId).put(time, value);
                }
                if ("meter_power".equals(record.getField())) {
                    meterMap.get(groupId).put(time, value);
                }
            }
        }
        if (meterMap.isEmpty()) {
            return map;
        }
        for (String groupId : groupIds) {
            for (Long time : meterMap.get(groupId).keySet()) {
                Double demandPower = demandMap.get(groupId).get(time);
                Double meterPower = meterMap.get(groupId).get(time);
                if (demandPower != null && meterPower != null) {
                    double controlPower = demandPower - meterPower;
                    if (map.get(groupId) != null) {
                        map.get(groupId).put(time, controlPower < 0 ? 0 : controlPower);
                    }
                }
            }
        }
        return map;
    }

    /**
     * 1.4.0 新增的 需量的算收益的power 从数据库查询
     *
     * @param rangeRequest : rangeRequest
     * @param projectId : projectId
     * @return : 分组id map<Long, Double> key: 时间月份的末尾 value: 降低需量值
     */
    @Override
    public Map<String, Map<Long, Double>> getDemandIncomePowerFromDb(
            RangeRequest rangeRequest, String projectId) {
        List<String> groupIds =
                groupService.queryEnableDemandIncome(projectId).stream()
                        .map(GroupEntity::getId)
                        .collect(Collectors.toList());
        if (groupIds.isEmpty()) {
            return Map.of();
        }
        // 直接从数据库里查询出来
        // just one groupId
        Map<String, Map<Long, Double>> resultMap = new HashMap<>();
        groupIds.forEach(
                groupId -> {
                    Map<Long, Double> groupMonthDemandIncomePowerMap = new HashMap<>();
                    // 改从数据库里面查询
                    List<GroupDemandMonthIncomeEntity> list =
                            groupDemandMonthIncomeMapper.selectList(
                                    new LambdaQueryWrapper<GroupDemandMonthIncomeEntity>()
                                            .eq(
                                                    GroupDemandMonthIncomeEntity::getProjectId,
                                                    projectId)
                                            .eq(GroupDemandMonthIncomeEntity::getGroupId, groupId)
                                            .between(
                                                    GroupDemandMonthIncomeEntity::getMonthStartTime,
                                                    rangeRequest.getStartDate(),
                                                    rangeRequest.getEndDate()));

                    // 查询出时间范围内 这个分组的每个月的 缓存数据
                    list.forEach(
                            groupDemandMonthIncomeEntity -> {
                                double demandIncomePower =
                                        groupDemandMonthIncomeEntity.getDemandIncomePower();
                                // 这个key 是月份的最后时间, 因为以前的getDemandIncomePower 这个方法 时间也是 月份的尾
                                // 应该是influxdb 返回的 这里保持统一
                                groupMonthDemandIncomePowerMap.put(
                                        groupDemandMonthIncomeEntity.getMonthStartTime(),
                                        demandIncomePower);
                            });

                    resultMap.put(groupId, groupMonthDemandIncomePowerMap);
                });
        return resultMap;
    }
}
