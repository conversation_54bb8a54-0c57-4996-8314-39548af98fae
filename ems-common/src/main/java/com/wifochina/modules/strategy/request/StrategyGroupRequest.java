package com.wifochina.modules.strategy.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * strategyRequest
 *
 * @since 4/19/2022 2:55 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "分组策略请求")
public class StrategyGroupRequest {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "申报需量控制功率")
    private Double demandPower;

    @ApiModelProperty(value = "容量控制功率")
    private Double controlPower;

    @ApiModelProperty(value = "需量控制功率,并网点控制目标功率(kW)")
    private Double gridControlPower;

    @ApiModelProperty(value = "并网点设计容量(kVA)")
    private Double pccDemandPower;

    // 2024-08-13 11:49:02 add 额定电压模式 (自动模式/手动模式 , 默认自动模式)
    @ApiModelProperty(value = "变压器额定电压模式")
    private String voltageMode;

    // 2024-08-13 11:49:02 add 额定电压值 (手动模式 , 才会设置这个值)
    @ApiModelProperty(value = "变压器额定电压")
    private Double voltagePower;

    @ApiModelProperty(value = "放电soc下限")
    private Double soc;

    @ApiModelProperty(value = "离网soc下限")
    private Double offGirdSoc;

    @ApiModelProperty(value = "离网soc上限")
    private Double offGridSocHighLimit;

    @ApiModelProperty(value = "并网soc上限")
    private Double chargeSocHighLimit;

    @ApiModelProperty(value = "防逆流 :true 为勾选 false为未勾选")
    private Boolean antiReflux;

    @ApiModelProperty(value = "防逆流最小值")
    private Double backFlowLimitPower;

    @ApiModelProperty(value = "仅在规定时间内充电")
    private Boolean chargeInAppointTime;

    @ApiModelProperty(value = "仅在规定时间内放电")
    private Boolean dischargeInAppointTime;

    @ApiModelProperty(value = "功率因数控制模式开关")
    private Boolean powerFactorControl;

    @ApiModelProperty(value = "功率因数优先模式，关闭时为有功功率优先")
    private Boolean powerFactorFirst;

    @ApiModelProperty(value = "功率因数控制值，0.9 - 1")
    private Double powerFactorControlValue;

    @ApiModelProperty(value = "月初需量")
    private Double monthControlPower;

    @ApiModelProperty(value = "策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式")
    private Integer strategyType;
}
