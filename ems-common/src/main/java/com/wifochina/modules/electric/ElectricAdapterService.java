package com.wifochina.modules.electric;

import com.wifochina.common.util.TimeContext;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.entity.ElectricPeriod;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.remedies.request.DataCalibrationEms;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 保存点收益的 适配器类 adapter <br>
 * Created on 2024/5/23 19:34.
 *
 * <AUTHOR>
 */
public interface ElectricAdapterService<T extends ElectricPeriod> {
    /**
     * 查询电收益 汇总的收益数据
     *
     * @param electricRequest : request
     * @param queryElectricResult: 回调
     * @return :ElectricProfitVO
     */
    ElectricProfitVO queryElectricTotalProfit(
            ElectricRequest electricRequest, QueryElectricResult queryElectricResult);

    /**
     * 查询电收益 每天的收益数据
     *
     * @param electricRequest : request
     * @param queryElectricsResult: 回调
     */
    void queryElectricEveryDayProfit(
            ElectricRequest electricRequest, QueryElectricsResult queryElectricsResult);

    /**
     * 设备电收益的 保存 for 数据补值方法 根据 context提供的 timePoint 构建 TimeContext对象
     *
     * @param context : context
     */
    default void saveElectricForDataCalibration(ElectricAdapterContext context) {
        TimeContext timeContext =
                TimeContext.getContext(context.project().getTimezone(), context.timePoint());
        Optional.ofNullable(context.periodPriceList())
                .ifPresent(periodList -> electricSaveForDataCalibration(context, timeContext));
    }

    /**
     * 保存 电收益
     *
     * @param context : ElectricAdapterContext
     */
    default void saveElectric(ElectricAdapterContext context) {
        TimeContext timeContext =
                TimeContext.getContext(context.project().getTimezone(), context.timePoint());
        Optional.ofNullable(context.periodPriceList())
                .ifPresent(
                        periodList -> {
                            deviceElectricSave(context, timeContext);
                            ammeterElectricSave(context, timeContext);
                        });
    }

    /**
     * 设备的 电收益缓存 save方法
     *
     * @param context : context
     * @param timeContext : timeContext
     */
    void deviceElectricSave(ElectricAdapterContext context, TimeContext timeContext);

    /**
     * 设备电收益的 保存 for 数据补值方法
     *
     * @param context : context
     * @param timeContext : timeContext
     */
    void electricSaveForDataCalibration(ElectricAdapterContext context, TimeContext timeContext);

    /**
     * 电表的 电收益缓存 save方法
     *
     * @param context : context
     * @param timeContext : timeContext
     */
    void ammeterElectricSave(ElectricAdapterContext context, TimeContext timeContext);

    /**
     * 计算段的收益
     *
     * @param price : price
     * @param electricPeriod : T
     * @param outDiff : 放电量
     * @param inDiff : 充电量
     */
    void calculatePeriod(
            ElectricPriceEntity price, T electricPeriod, Double outDiff, Double inDiff);

    /**
     * 计算时段收益 汇聚到 ElectricProfitVO 对象 <br>
     * 历史遗留问题
     *
     * @param price : price
     * @param electricProfitVO :electricProfitVO
     * @param outDiff : 放电量
     * @param inDiff : 充电量
     */
    void calculatePeriodWithElectricProfitVo(
            ElectricPriceEntity price,
            ElectricProfitVO electricProfitVO,
            Double outDiff,
            Double inDiff);

    /**
     * 电价时段 类型 区分 尖峰苹果(原来的) 和 新增的 海外 动态自定义电价方式
     *
     * @return : ElectricPriceTypeEnum.value
     * @see com.wifochina.common.constants.ElectricPriceTypeEnum
     */
    Set<String> type();

    interface ElectricAdapterContext {

        long timePoint();

        default String groupId() {
            return null;
        }

        default DataCalibrationEms dataCalibration() {
            return null;
        }

        /**
         * 电价 时段 的实体列表
         *
         * @return : list
         */
        default List<ElectricPriceEntity> periodPriceList() {
            return null;
        }

        /**
         * 项目entity
         *
         * @return : entity
         */
        ProjectEntity project();

    }

    /** 返回聚合后的一个对象 */
    interface QueryElectricResult {
        /**
         * 尖峰平谷的 后置处理
         *
         * @param t :ElectricEntity
         * @return : ElectricProfitVO
         */
        ElectricProfitVO peakValleysPeriodPostProcessor(ElectricEntity t);

        /**
         * 动态时段 的 后置处理
         *
         * @param t :ElectricDynamicPeriodEntity
         * @return : ElectricProfitVO
         */
        ElectricProfitVO dynamicPeriodPostProcessor(ElectricDynamicPeriodEntity t);
    }

    /** 返回 List 为主 */
    interface QueryElectricsResult {
        /**
         * 尖峰平谷的 后置处理
         *
         * @param t :List<ElectricEntity>
         */
        void peakValleysPeriodPostProcessor(List<ElectricEntity> t);

        /**
         * 动态时段 的 后置处理
         *
         * @param t :List<ElectricDynamicPeriodEntity>
         */
        void dynamicPeriodPostProcessor(List<ElectricDynamicPeriodEntity> t);
    }
}
