package com.wifochina.modules.alarm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.alarm.entity.AlarmNotificationEntity;
import com.wifochina.modules.alarm.mapper.AlarmNotificationMapper;
import com.wifochina.modules.alarm.service.AlarmNotificationService;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 告警通知配置 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class AlarmNotificationServiceImpl extends ServiceImpl<AlarmNotificationMapper, AlarmNotificationEntity>
        implements AlarmNotificationService {

    @Override
    public List<AlarmNotificationEntity> getByAlarmId(String alarmId) {
        LambdaQueryWrapper<AlarmNotificationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmNotificationEntity::getAlarmId, alarmId);
        queryWrapper.orderByDesc(AlarmNotificationEntity::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public boolean addAlarmNotification(AlarmNotificationEntity alarmNotification) {
        return this.save(alarmNotification);
    }

    @Override
    public boolean updateAlarmNotification(AlarmNotificationEntity alarmNotification) {
        return this.updateById(alarmNotification);
    }

    @Override
    public boolean deleteAlarmNotification(String id) {
        return this.removeById(id);
    }
}
