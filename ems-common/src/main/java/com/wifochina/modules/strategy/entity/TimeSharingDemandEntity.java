package com.wifochina.modules.strategy.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.wifochina.modules.BaseEntity;
import com.wifochina.modules.strategytemplate.common.GoTimeSlotTransformAble;
import com.wifochina.modules.strategytemplate.common.YearlyStrategyTransformAble;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalTime;

/**
 * BackFlowLimitRequest
 *
 * @since 2025-07-23 16:06:50
 * <AUTHOR>
 * @version 1.0
 */
@Data
@TableName("t_time_sharing_demand")
@ApiModel(value = "分时demandItem请求")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TimeSharingDemandEntity extends BaseEntity implements GoTimeSlotTransformAble {

    private Integer id;

    private String projectId;

    private String groupId;

    @ApiModelProperty(value = "系统主策略id")
    private Integer systemStrategyId;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @ApiModelProperty(value = "需量 true 为勾选 false 为未勾选")
    private Boolean demandItemController;

    @ApiModelProperty(value = "月初需量")
    private Double demandMonthControlPower;

    @ApiModelProperty(value = "防逆流最小值")
    private Double demandControlPower;
}
