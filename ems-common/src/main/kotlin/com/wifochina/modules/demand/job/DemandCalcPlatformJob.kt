package com.wifochina.modules.demand.job

import com.wifochina.common.mdc.MdcKotlinContext
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.demand.service.adjustmodel.DemandControlAdjustModelChooser
import com.wifochina.modules.demand.service.adjustmodel.DemandControlAdjustModelService
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.project.entity.ProjectEntity
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.quartz.JobExecutionContext
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.scheduling.quartz.QuartzJobBean
import org.springframework.util.StopWatch
import java.time.LocalDateTime
import java.time.ZoneId

private val log = KotlinLogging.logger { }

/**
 * Created on 2025/1/9 10:38.
 * <AUTHOR>
 */
//@Component
class DemandCalcPlatformJob(
    val groupService: GroupService,
    val demandControlAdjustModelChooser: DemandControlAdjustModelChooser,
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
) : QuartzJobBean() {

    override fun executeInternal(context: JobExecutionContext) {
        val timezone = context.jobDetail.jobDataMap[EmsConstants.TIME_ZONE_KEY].toString();
        DemandCalcHolder.holder[timezone]?.takeIf { it.isNotEmpty() }?.let { calcProject ->
            val startDateTime = LocalDateTime.now(ZoneId.of(timezone))
            try {
                runBlocking {
                    withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                        calcProject
                            .forEach { project ->
                                launch {
                                    // 查询开启了需量控制的 分组的列表
                                    var demandGroupEntities = groupService.queryEnableDemandControl(project.id);
                                    log.info {
                                        "DemandCalcJob 开始执行 时间: $startDateTime, 项目Id:${project.id} , 项目名称:${project.projectName} , 需量分组id列表:${
                                            demandGroupEntities.joinToString { "${it.id}-${it.name}," }
                                        }"
                                    }

                                    val stopwatch = StopWatch()
                                    stopwatch.start()
                                    //core
                                    for (groupEntity in demandGroupEntities) {
                                        demandControlAdjustModelChooser // 根据当前控制需量调整模式 来选择策略触发
                                            .chooseAdjustModel(groupEntity.demandControlAdjustModel)
                                            .adjustExecute(object :
                                                               DemandControlAdjustModelService.HandlerAdjustModelHolder {
                                                override fun group(): GroupEntity {
                                                    return groupEntity
                                                }

                                                override fun project(): ProjectEntity {
                                                    return project
                                                }
                                            })
                                    }
                                    val endTime = LocalDateTime.now(ZoneId.of(project.timezone))
                                    stopwatch.stop()
                                    log.info {
                                        "DemandCalcJob 执行完毕 时间: $endTime, 项目Id:${project.id} , 项目名称:${project.projectName} , 耗时:${stopwatch.totalTimeSeconds}s "
                                    }

                                }
                            }
                    }
                }
            } catch (e: Exception) {
                log.error { "....job错误 ${e.message}" }
            }
        }
    }
}